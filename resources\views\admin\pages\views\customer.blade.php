@extends('admin.layout')

@section('title', __('admin_labels.customer_details'))

@section('content')
<div class="container py-4">
    <!-- Customer Profile Section -->
    <div class="card border-0 shadow-sm rounded-3 mb-4">
        <div class="card-header bg-gradient py-3" style="background: linear-gradient(135deg, #6f42c1 0%, #b73fbb 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-white">
                    <i class="bx bx-user-circle me-2"></i>
                    {{ __('admin_labels.customer_details') }}
                </h5>
                <span class="badge bg-white text-dark">
                    #{{ $customer->id }}
                </span>
            </div>
        </div>

        <div class="card-body p-4">
            <div class="row">
                <!-- Customer Info -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold">
                                <i class="bx bx-info-circle me-2"></i>
                                {{ __('admin_labels.user_info') }}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0 me-3">
                                <div class="avatar avatar-xl bg-light rounded-circle d-flex align-items-center justify-content-center">
                                            <i class="bx bx-user fs-4"></i>
                                        </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="mb-1">{{ $customer->username }}</h5>
                                    <p class="text-muted mb-2">{{ $customer->email }}</p>
                                
                                    {!! $status !!}
                                  
                                </div>
                            </div>

                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="p-2 bg-light rounded-2">
                                        <small class="text-muted d-block">{{ __('admin_labels.mobile') }}</small>
                                        <span class="fw-medium">{{ $customer->mobile }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-2 bg-light rounded-2">
                                        <small class="text-muted d-block">{{ __('admin_labels.city') }}</small>
                                        <span class="fw-medium">{{ $customer->address?->first()->city->{'name_' . app()->getLocale()} ?? '-' }}</span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="p-2 bg-light rounded-2">
                                        <small class="text-muted d-block">{{ __('admin_labels.join_at') }}</small>
                                        <span class="fw-medium">{{ $customer->created_at?->format('M d, Y \a\t H:i') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics (if needed) -->
                <div class="col-md-6 mt-3 mt-md-0">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold">
                                <i class="bx bx-stats me-2"></i>
                                {{ __('admin_labels.activity') }}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="p-3 bg-light rounded-3 text-center">
                                        <h4 class="mb-1 fw-bold">{{ $cliamedCount }}</h4>
                                        <small class="text-muted">{{ __('admin_labels.total_claims') }}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3 bg-light rounded-3 text-center">
                                        <h4 class="mb-1 fw-bold">{{ $redeemedCount }}</h4>
                                        <small class="text-muted">{{ __('admin_labels.redeemed') }}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3 bg-light rounded-3 text-center">
                                        <h4 class="mb-1 fw-bold">{{ $pendingCount }}</h4>
                                        <small class="text-muted">{{ __('admin_labels.pending') }}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3 bg-light rounded-3 text-center">
                                        <h4 class="mb-1 fw-bold">{{ $favoriteCount }}</h4>
                                        <small class="text-muted">{{ __('admin_labels.favorites') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Claimed Offers Section -->
    <div class="card border-0 shadow-sm rounded-3">
        <div class="card-header bg-gradient py-3" style="background: linear-gradient(135deg, #6f42c1 0%, #b73fbb 100%);">
            <h5 class="mb-0 text-white">
                <i class="bx bx-gift me-2"></i>
                {{ __('admin_labels.claimed_offers') }}
            </h5>
        </div>

        <div class="card-body p-0">
            @if($claimedOffers->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="50">#</th>
                            <th>{{ __('admin_labels.offer') }}</th>
                            <th>{{ __('admin_labels.store_name') }}</th>
                            <th>{{ __('admin_labels.claim_date') }}</th>
                            <th>{{ __('admin_labels.status') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($claimedOffers as $index => $claim)
                        <tr>
                            <td class="fw-medium">{{ $index + 1 }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($claim->offer->images->first())
                                    <div class="flex-shrink-0 me-2">
                                        <img src="{{ getMediaImageUrl($claim->offer->images->first()->image_url) }}" 
                                             class="rounded-2 border" 
                                             width="60" 
                                             style="object-fit: cover;">
                                    </div>
                                    @endif
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">{{ $claim->offer->{'title_' . app()->getLocale()} }}</h6>
                                        <small class="text-muted">{{ $claim->offer->store->{'name_' . app()->getLocale()} }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ $claim->offer->store->{'name_' . app()->getLocale()} }}</td>
                            <td>{{ $claim->created_at?->format('M d, Y H:i') }}</td>
                            <td>
                                @if($claim->is_redeemed)
                                <span class="badge bg-success">
                                    <i class="bx bx-check-circle me-1"></i> {{ __('admin_labels.redeemed') }}
                                </span>
                                @else
                                <span class="badge bg-warning">
                                    <i class="bx bx-time me-1"></i> {{ __('admin_labels.pending') }}
                                </span>
                                @endif
                            </td>
                            
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            @if($claimedOffers->hasPages())
            <div class="d-flex justify-content-center mt-4 pb-3">
                {{ $claimedOffers->links() }}
            </div>
            @endif
            @else
            <div class="text-center py-5">
                <div class="avatar avatar-xl bg-light rounded-circle mb-3">
                    <i class="bx bx-package text-muted fs-4"></i>
                </div>
                <h5 class="mb-2">{{ __('admin_labels.no_claims_found') }}</h5>
                <p class="text-muted">{{ __('admin_labels.no_claims_description') }}</p>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        border-radius: 0.75rem;
        overflow: hidden;
    }
    .avatar {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .avatar-xl {
        width: 80px;
        height: 80px;
    }
    .table-hover tbody tr:hover {
        background-color: rgba(111, 66, 193, 0.05);
    }
    .badge {
        padding: 0.35em 0.65em;
        font-weight: 500;
    }
    .rounded-2 {
        border-radius: 0.5rem;
    }
    .rounded-3 {
        border-radius: 0.75rem;
    }
</style>
@endpush