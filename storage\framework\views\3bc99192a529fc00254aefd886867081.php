<?php $__env->startSection('title'); ?>
<?php echo e(labels('admin_labels.spotlight_ads', 'spotlight ads')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<?php if($errors->any()): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <strong><i class="bx bx-error-circle me-2"></i><?php echo e(__('admin_labels.validation_failed', 'Validation failed')); ?></strong>
    <ul class="mt-2 mb-0 ps-3">
        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li><?php echo e($error); ?></li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>
<div class="d-flex row align-items-center">
    <div class="col-md-6 col-xl-6 page-info-title">
        <h3><?php echo e(labels('admin_labels.spotlight_ads', 'spotlight ads')); ?></h3>
        <p class="sub_title">
            <?php echo e(__('admin_labels.spotlight_ads_explanation')); ?>

        </p>
    </div>
    <div class="col-md-6 col-xl-6 d-flex justify-content-end" dir='ltr'>
        <nav aria-label="breadcrumb" class="float-end">
            <ol class="breadcrumb">
                <i class='bx bx-home-smile'></i>
                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.home')); ?>"><?php echo e(labels('admin_labels.home', 'Home')); ?></a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <?php echo e(labels('admin_labels.spotlight_ads', 'spotlight ads')); ?>

                </li>
            </ol>
        </nav>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <form class="form-horizontal form-submit-event submit_form" action="<?php echo e(route('spotlight.store')); ?>"
                method="POST">
                <?php echo csrf_field(); ?>
                <div class="card-body">
                    <h5 class="mb-3">
                        <?php echo e(labels('admin_labels.spotlight_ads', 'spotlight ads')); ?>

                    </h5>
                    <div class="row mt-2">


                        <div class="col-md-6">
                            <label><?php echo e(__('admin_labels.ad_type')); ?> <span class="text-danger">*</span></label>
                            <select name="advertisement_type" id="adType" class="form-select" required onchange="toggleAdOptions()">
                                <option value=""><?php echo e(__('admin_labels.select_type')); ?></option>
                                <option value="OFFER"><?php echo e(__('admin_labels.offer')); ?></option>
                                <option value="STORE"><?php echo e(__('admin_labels.stores')); ?></option>
                            </select>
                        </div>

                        <div class="col-md-6" id="offerSelect" style="display:none;">
                            <label><?php echo e(__('admin_labels.select_offer')); ?></label>
                            <select name="offer_id" class="form-select">
                                <?php $__currentLoopData = $offers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $offer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($offer->id); ?>"><?php echo e($offer->title); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <div class="col-md-6" id="storeSelect" style="display:none;">
                            <label><?php echo e(__('admin_labels.select_store')); ?></label>
                            <select name="store_id" class="form-select" required>
                                <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($store->id); ?>"><?php echo e($store->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="mb-2 mt-2"
                                for=""><?php echo e(labels('admin_labels.start_date', 'Start Date')); ?>

                                <span class='text-asterisks text-sm'>*</span></label>
                                <input type="datetime-local" name="start_date" min="<?php echo e(date('Y-m-d\TH:i')); ?>" class="form-control" required>
                                </div>
                        <div class="col-md-6 mb-3">
                            <label class="mb-2 mt-2" for=""><?php echo e(labels('admin_labels.end_date', 'End Date')); ?>

                                <span class='text-asterisks text-sm'>*</span></label>
                                <input type="datetime-local" name="end_date" min="<?php echo e(date('Y-m-d\TH:i')); ?>" class="form-control" required>
                                </div>
                    </div>


                    <div class="d-flex justify-content-end">
                        <button type="reset"
                            class="btn mx-2 reset_button"><?php echo e(labels('admin_labels.reset', 'Reset')); ?></button>
                        <button type="submit"
                            class="btn btn-primary submit_button"><?php echo e(labels('admin_labels.add_spotlight_ads', 'Add Spotlight')); ?></button>
                    </div>

                </div>

            </form>
        </div>
        
        <div
            class="col-md-12 mt-4 <?php echo e($user_role == 'super_admin' || $logged_in_user->hasPermissionTo('view promo_code') ? '' : 'd-none'); ?>">
            <section class="overview-data">
                <div class="card content-area p-4 ">
                    <div class="row align-items-center d-flex heading mb-5">
                        <div class="col-md-12">
                            <div class="row">
                            
                                <div class="col-sm-12 d-flex justify-content-end " >
                                    <div class="input-group me-2 search-input-grp " dir="ltr">
                                        <span class="search-icon"><i class='bx bx-search-alt'></i></span>
                                        <input type="text" data-table="admin_promocode_table"
                                            class="form-control searchInput" placeholder="<?php echo e(labels('admin_labels.search', 'Search')); ?>...">
                                        <span
                                            class="input-group-text"><?php echo e(labels('admin_labels.search', 'Search')); ?></span>
                                    </div>
                                    <a class="btn me-2" id="tableFilter" data-bs-toggle="offcanvas"
                                        data-bs-target="#columnFilterOffcanvas" data-table="admin_promocode_table"
                                        dateFilter='false' StatusFilter='true'><i class='bx bx-filter-alt'></i></a>
                                    <a class="btn me-2" id="tableRefresh" data-table="admin_promocode_table" ><i
                                            class='bx bx-refresh'></i></a>
                                    <div class="dropdown">
                                        <a class="btn dropdown-toggle export-btn" type="button"
                                            id="exportOptionsDropdown" data-bs-toggle="dropdown"
                                            aria-expanded="false">
                                            <i class='bx bx-download'></i>
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="exportOptionsDropdown">
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_promocode_table','csv')">CSV</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_promocode_table','json')">JSON</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_promocode_table','sql')">SQL</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_promocode_table','excel')">Excel</button></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                    
                        <div class="col-md-12">
                            <div class="pt-0">
                                <div class="table-responsive">
                                    <table class='table' id="admin_promocode_table" data-toggle="table"
                                        data-loading-template="loadingTemplate"
                                        data-url="<?php echo e(route('spotlight.list')); ?>" data-click-to-select="true"
                                        data-side-pagination="server" data-pagination="true"
                                        data-page-list="[5, 10, 20, 50, 100, 200]" data-search="false"
                                        data-show-columns="false" data-show-refresh="false"
                                        data-trim-on-search="false" data-sort-name="id" data-sort-order="desc"
                                        data-mobile-responsive="true" data-toolbar="" data-show-export="false"
                                        data-maintain-selected="true" data-export-types='["txt","excel"]'
                                        data-query-params="PromoqueryParams">
                                        <thead>
                                            <tr>
                                               
                                                <th data-field="id" data-sortable="true" data-visible='true'>
                                                <?php echo e(labels('admin_labels.id', 'ID')); ?>

                                                <th data-field="type_name" data-sortable="false">
                                                <?php echo e(labels('admin_labels.name', 'Name')); ?>

                                            </th>

                                        
                                            <th data-field="created_by" data-sortable="false">
                                                <?php echo e(labels('admin_labels.created_by', 'Created By')); ?>

                                            </th>
                                            <th data-field="type" data-sortable="false">
                                                <?php echo e(labels('admin_labels.type', 'Type')); ?>

                                            </th>

                                          
                                            <th data-field="status" data-sortable="false">
                                                <?php echo e(labels('admin_labels.status', 'Status')); ?>

                                            </th>
                                            <th data-field="start_date" data-sortable="false">
                                                <?php echo e(labels('admin_labels.start_date', 'Start Date')); ?>

                                            </th>
                                            <th data-field="end_date" data-sortable="false">
                                                <?php echo e(labels('admin_labels.end_date', 'End Date')); ?>

                                            </th>
                                            <th data-field="operate" data-sortable="false">
                                                <?php echo e(labels('admin_labels.action', 'Action')); ?>

                                            </th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<script>
    function toggleAdOptions() {
        var type = document.getElementById('adType').value;
        document.getElementById('offerSelect').style.display = type === 'OFFER' ? 'block' : 'none';
        document.getElementById('storeSelect').style.display = type === 'STORE' ? 'block' : 'none';
    }
</script>
<?php echo $__env->make('admin/layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/pages/tables/manage_spotlight.blade.php ENDPATH**/ ?>