@extends('admin.layout')

@section('title', __('admin_labels.spotlight_details'))

@section('content')
<div class="container py-4">
    <div class="card border-0 shadow-sm rounded-3 overflow-hidden">
        <!-- Header with gradient background -->
        <div class="card-header bg-gradient py-3" style="background: linear-gradient(135deg, #6f42c1 0%, #b73fbb 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-white">
                    <i class="bx bx-star me-2"></i>
                    {{ __('admin_labels.spotlight_details') }}
                </h5>
                <span class="badge bg-white text-dark">
                    #{{ $ad->id }}
                </span>
            </div>
        </div>

        <div class="card-body p-4">
            <!-- Main Info Section -->
            <div class="row g-4 mb-4">
                <!-- Basic Info Card -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold">
                                <i class="bx bx-info-circle me-2"></i>
                                {{ __('admin_labels.basic_info') }}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.ad_type') }}</span>
                                <span class="fw-medium">{{ $ad->advertisement_type->label() }}</span>
                            </div>
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.created_by') }}</span>
                                <span class="fw-medium">
                                    @if($ad->created_by_type === 'ADMIN')
                                        <span class="badge bg-primary bg-opacity-10 text-primary">
                                            {{ __('admin_labels.created_by_admin') }}
                                        </span>
                                    @else
                                        <span class="badge bg-info bg-opacity-10 text-info">
                                            {{ __('admin_labels.created_by_store') }}
                                        </span>
                                    @endif
                                </span>
                            </div>
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.duration') }}</span>
                                <span class="fw-medium">
                                    {{ $ad->start_date }} → {{ $ad->end_date }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Package Info Card (if exists) -->
                @if($ad->created_by_type === 'STORE' && $ad->package)
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold">
                                <i class="bx bx-package me-2"></i>
                                {{ __('admin_labels.package_info') }}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.package') }}</span>
                                <span class="fw-medium">{{ $ad->package->title ?? '-' }}</span>
                            </div>
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.price') }}</span>
                                <span class="fw-medium">{{ $ad->package->price ?? '-' }}</span>
                            </div>
                            <div class="d-flex">
                                <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.duration') }}</span>
                                <span class="fw-medium">{{ $ad->package->duration_in_days ?? '-' }} {{ __('admin_labels.days') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- User/Store Section -->
            <div class="row g-4 mb-4">
                @if($creatorUser)
                <!-- User Info Card -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold">
                                <i class="bx bx-user me-2"></i>
                                {{ __('admin_labels.user_info') }}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;">ID</span>
                                <span class="fw-medium">{{ $creatorUser->id }}</span>
                            </div>
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.name') }}</span>
                                <span class="fw-medium">{{ $creatorUser->username }}</span>
                            </div>
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.email') }}</span>
                                <span class="fw-medium">{{ $creatorUser->email }}</span>
                            </div>
                            <div class="d-flex">
                                <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.phone') }}</span>
                                <span class="fw-medium">{{ $creatorUser->mobile ?? '-' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                @if($store)
                <!-- Store Info Card -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold">
                                <i class="bx bx-store me-2"></i>
                                {{ __('admin_labels.store_info') }}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 120px;">ID</span>
                                        <span class="fw-medium">{{ $store->id }}</span>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.name_en') }}</span>
                                        <span class="fw-medium">{{ $store->name_en }}</span>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.name_ar') }}</span>
                                        <span class="fw-medium">{{ $store->name_ar }}</span>
                                    </div>
                                    <div class="d-flex">
                                        <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.mobile') }}</span>
                                        <span class="fw-medium">{{ $store->user?->mobile??'-' }}</span>
                                    </div>
                                </div>
                                @if($store->logo)
                                <div class="flex-shrink-0 ms-3">
                                    <img src="{{ getMediaImageUrl($store->logo) }}" class="rounded-circle border" width="60" height="60" style="object-fit: cover;">
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Offer Info Section (if exists) -->
            @if($offer)
            <div class="row g-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold">
                                <i class="bx bx-gift me-2"></i>
                                {{ __('admin_labels.offer_info') }}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 120px;">ID</span>
                                        <span class="fw-medium">{{ $offer->id }}</span>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.name_en') }}</span>
                                        <span class="fw-medium">{{ $offer->title_en }}</span>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.name_ar') }}</span>
                                        <span class="fw-medium">{{ $offer->title_ar }}</span>
                                    </div>
                                    <div class="d-flex">
                                        <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.price') }}</span>
                                        <span class="fw-medium">{{ $offer->price }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 120px;">{{ __('admin_labels.expire_date') }}</span>
                                        <span class="fw-medium">{{ $offer->expire_date }}</span>
                                    </div>
                                    @if($offer->images->first())
                                    <div class="mt-3">
                                        <img src="{{ getMediaImageUrl($offer->images->first()->image_url) }}" class="rounded border" style="max-height: 120px;">
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .bg-gradient {
        background-clip: padding-box;
    }
    .card {
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }
    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1) !important;
    }
    .badge {
        padding: 0.35em 0.65em;
        font-weight: 500;
    }
</style>
@endpush