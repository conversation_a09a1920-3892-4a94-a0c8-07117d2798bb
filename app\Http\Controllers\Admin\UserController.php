<?php

namespace App\Http\Controllers\Admin;

use App\Enums\UserRole;
use App\Http\Requests\StoreReqiserRequest;
use App\Models\awfarly\Category;
use App\Models\awfarly\Favorite;
use App\Models\awfarly\OfferClaim;
use App\Models\awfarly\Role;
use App\Models\awfarly\Store;
use App\Models\awfarly\User;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\File;
use App\Models\Media;
use Exception;

class UserController extends Controller
{
    public function login()
    {
        return view('admin/pages/forms/login');
    }
    public function seller_login()
    {
        return view('seller/pages/forms/login');
    }
    public function store_employee_login()
    {
        return view('store_employee/pages/forms/login');
    }

    public function logout(Request $request)
    {
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/admin/login')->withHeaders([
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    public function seller_logout(Request $request)
    {
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/seller/login')->withHeaders([
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }
    public function delivery_boy_logout(Request $request)
    {
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/delivery_boy/login')->withHeaders([
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    public function authenticate(Request $request)
    {
        $formFields = $request->validate([
            'password' => 'required',
            'mobile' => 'required',
        ]);

        if (auth()->attempt($formFields)) {

            $user = User::with('role')
                ->where('is_active', 1)
                ->find(Auth::user()->id);
            if ($user) {

                if ($user->role->name->value == UserRole::seller->value) {
                    $response = ['message' => __('admin_labels.login_successful'), 'location' => '/seller/home'];
                    return response()->json($response);
                } elseif ($user->role->name->value == UserRole::super_admin->value || $user->role->name == 'admin' || $user->role->name == 'editor') {
                    $response = ['message' => __('admin_labels.login_successful'), 'location' => '/admin/home'];
                    return response()->json($response);
                } else {
                    $response = ['errors' => ['role' => ['You do not have access to this panel']]];
                    return response()->json($response, 422);
                }
            } else {
                $response = ['errors' => ['account' => ['Your account is not activated yet. Please wait for activation.']]];
                return response()->json($response, 422);
            }
        } else {
            $response = ['errors' => ['email' => ['Invalid credentials']]];
            return response()->json($response, 422);
        }
    }


    public function edit(User $user)
    {
        return view('admin.pages.forms.account', ['user' => $user]);
    }


    public function update(Request $request, $id)
    {

        $validator = Validator::make($request->all(), [
            'username' => ['required'],
            'email' => ['required'],
            'mobile' => 'required',
        ]);

        if (!empty($request->input('old_password')) || !empty($request->input('new_password'))) {
            $validator = Validator::make($request->all(), [
                'old_password' => 'required',
                'new_password' => ['required', 'confirmed'],
                'image' => 'image|mimes:jpeg,gif,jpg,png',
            ]);
        }

        if ($validator->fails()) {
            if ($request->ajax()) {
                throw ValidationException::withMessages($validator->errors()->all());
            }

            return redirect()->back()->withErrors($validator)->withInput();
        }

        $user = User::find($id);

        // Check if the old password matches the one in the database
        if (!empty($request->input('old_password'))) {
            if (!Hash::check($request->old_password, $user->password)) {
                if ($request->ajax()) {
                    return response()->json([
                        'message' => labels('admin_labels.incorrect_old_password', 'The old password is incorrect.')
                    ], 422);
                }
                return redirect()->back()->withErrors([
                    'old_password' => labels('admin_labels.incorrect_old_password', 'The old password is incorrect.')
                ])->withInput();
            }
        }

        $userImgPath = public_path(config('constants.USER_IMG_PATH'));

        if (!File::exists($userImgPath)) {
            File::makeDirectory($userImgPath, 0755, true);
        }


        if ($validator->fails()) {
            return response()->json(['error' => true, 'message' => $validator->errors()], 400);
        }

        //----------------- image upload code ----------------------------

        $mediaItem = [];
        try {


            $media_storage_settings = fetchDetails('storage_types', ['is_default' => 1], '*');

            $disk = isset($media_storage_settings) && !empty($media_storage_settings) ? $media_storage_settings[0]->name : 'public';
            if ($request->hasFile('image')) {

                // Specify the path and disk from which you want to delete the file
                if ($disk == 's3') {
                    $path = $request->input('edit_image');
                } else {
                    $path = 'store_images/' . $request->input('edit_image'); // Example path to the file you want to delete
                }

                //Call the removeFile method to delete the file
                removeMediaFile($path, $disk);

                $mediaFile = $request->file('image');

                $mediaItem = $user->addMedia($mediaFile)
                    ->sanitizingFileName(function ($fileName) use ($user) {
                        // Replace special characters and spaces with hyphens
                        $sanitizedFileName = strtolower(str_replace(['#', '/', '\\', ' '], '-', $fileName));

                        // Generate a unique identifier based on timestamp and random component
                        $uniqueId = time() . '_' . mt_rand(1000, 9999);

                        $extension = pathinfo($sanitizedFileName, PATHINFO_EXTENSION);
                        $baseName = pathinfo($sanitizedFileName, PATHINFO_FILENAME);

                        return "{$baseName}-{$uniqueId}.{$extension}";
                    })
                    ->toMediaCollection('user_image', $disk);

                $media_list = $user->getMedia('user_image');
                $media_url = $media_list[0]->getUrl();
            }
            if (isset($mediaItem->file_name)) {
                $image = $disk == 's3' ? (isset($media_url) ? $media_url : '') : (isset($mediaItem->file_name) ? '/' . $mediaItem->file_name : '');
            } else {
                $image = $user->image;
            }
        } catch (Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ]);
        }


        // Update the user's other details
        $formFields = [
            'username' => $request->username,
            'email' => $request->email,
            'mobile' => $request->mobile,
            'address' => $request->address,
            'image' => $image,
            'disk' => $disk,
        ];
        $user->update($formFields);
        if (!empty($mediaItem)) {

            Media::destroy($mediaItem->id);
        }

        // Update the password if a new password is provided
        if ($request->new_password) {
            $user->password = Hash::make($request->new_password);
            $user->save();
        }

        if ($request->ajax()) {
            return response()->json(['message' => labels('admin_labels.profile_details_updated_successfully', 'Profile details updated successfully!')]);
        }

        return back()->with('message', labels('admin_labels.profile_details_updated_successfully', 'Profile details updated successfully!'));
    }


    public function updatePhoto(Request $request, $id)
    {

        if ($request->hasFile('upload')) {
            $formFields['photo'] = $request->file('upload')->store('photos', 'public');
            User::find($id)->update($formFields);
            session()->flash('success', 'Image Upload successfully');
            return back()->with('message', labels('admin_labels.profile_picture_updated_successfully', 'Profile picture update Successfully!'));
        }
    }
    public function destroy($id)
    {
        $user = User::find($id);

        if ($user) {
            $user->delete();
            return response()->json([
                'error' => false,
                'message' => labels('admin_labels.user_deleted_successfully', 'User deleted successfully!')
            ]);
        } else {
            return response()->json(['error' => labels('admin_labels.data_not_found', 'Data Not Found')]);
        }
    }


    public function store(Request $request, User $user)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => ['required'],
            'last_name' => ['required'],
            'email' => ['required', 'email'],
            'password' => 'required|confirmed|min:6'
        ]);
        if ($validator->fails()) {
            // Return the validation errors as a JSON response
            return response()->json(['errors' => $validator->errors()], 422);
        }
        $formFields['first_name'] = $request->first_name;
        $formFields['last_name'] = $request->last_name;
        $formFields['email'] = $request->email;
        $formFields['password'] = bcrypt($request->password);
        $formFields['photo'] = "photos/no-image.png";
        $formFields['role_id'] = 2;
        $formFields['status'] = 1;

        $user = User::create($formFields);

        auth()->login($user);

        if ($request->ajax()) {
            return response()->json(['message' => labels('admin_labels.registered_successfully', 'Registered Successfully!')]);
        } else {
            return redirect('/login')->with('message', labels('admin_labels.registered_successfully', 'Registered Successfully!'));
        }
    }

    public function searchUser(Request $request)
    {
        $search_term = trim($request->input('search'));
        $roleId = Role::where('name', UserRole::CUSTOMER)->value('id');
        $users = User::select('id', 'username', 'is_active')
            ->where('username', 'like', '%' . $search_term . '%')
            ->where('is_active', '1')
            ->where('role_id', $roleId)
            ->get();
        Log::info("from ser", [$users]);
        $data = [];
        foreach ($users as $user) {
            $data[] = [
                "id" => $user->id,
                "text" => $user->username,
            ];
        }

        return response()->json($data);
    }
    //edit it
    public function searchSeller(Request $request)
    {
        $search_term = trim($request->input('search'));

        $stores = Store::select('user_id', 'name_' . app()->getLocale() . ' as name')
            ->where('name_en', 'like', '%' . $search_term . '%')
            ->orWhere('name_ar', 'like', '%' . $search_term . '%')
            ->get();

        $data = [];
        foreach ($stores as $store) {
            $data[] = [
                "id" => $store->user_id,
                "text" => $store->name,
            ];
        }

        return response()->json($data);
    }
    ///////////////////////////////////////////////////////

    /**
     * Return the view for managing customers.
     */
    public function customers()
    {
        // Get the customer role ID 
        $roleId = Role::where('name', UserRole::CUSTOMER)->value('id');

        // Count different customer types
        $allCustomers     = User::where('role_id', $roleId)->count();
        $activeCustomers  = User::where('role_id', $roleId)->where('is_active', 1)->where('is_banned', 0)->where('is_deleted', 0)->count();
        $bannedCustomers  = User::where('role_id', $roleId)->where('is_banned', 1)->where('is_deleted', 0)->count();
        $deletedCustomers = User::where('role_id', $roleId)->where('is_deleted', 1)->count();

        return view('admin.pages.tables.customers', compact(
            'allCustomers',
            'activeCustomers',
            'bannedCustomers',
            'deletedCustomers'
        ));
    }

    /**
     * Fetch and return a paginated list of customers.
     * Supports search, sorting, and filtering by status.
     * 
     * @param  \App\Http\Requests\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomersList(Request $request)
    {
        try {
            // Inputs with default fallbacks
            $search   = trim($request->input('search'));
            $offset   = $search || $request->has('pagination_offset') ? (int) $request->input('pagination_offset', 0) : 0;
            $limit    = (int) $request->input('limit', 10);
            $sort     = $request->input('sort', 'id');
            $order    = $request->input('order', 'asc');
            $status   = $request->input('status', '');

            // Get the customer role ID
            $roleId = Role::where('name', UserRole::CUSTOMER)->value('id');

            // Build query for customers with that role
            $query = User::where('role_id', $roleId);

            // Add search filtering
            if ($search !== '') {
                $query->where(function ($q) use ($search) {
                    $q->where('id', $search)
                        ->orWhere('username', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('mobile', 'like', "%{$search}%");
                });
            }

            // Filter by active status if given
            if ($status !== '') {
                $query->where('is_active', $status);
            }

            // Clone query to get total count
            $total = (clone $query)->count();

            // Apply sorting and pagination
            $customers = $query->orderBy($sort, $order)
                ->offset($offset)
                ->limit($limit)
                ->get();

            $rows = [];

            foreach ($customers as $customer) {
                // Action button (View)
                $showUrl = route('admin.customers.show', ['id' => $customer->id]);
                $action = '<a href="' . $showUrl . '" class="btn single_action_button" title="View Customer">
            <i class="bx bxs-show"></i>
            </a>';

                $banned = '<div class="form-check form-switch">
            <input
            class="form-check-input toggle-ban"
            type="checkbox"
            data-id="' . $customer->id . '" ' . ($customer->is_banned == 1 ? 'checked' : '') . ' >
            </div>';

                // Status label
                if ($customer->is_deleted) {
                    $statusBadge = '<span class="badge bg-danger">' . labels('admin_labels.deleted', 'Deleted') . '</span>';
                } elseif ($customer->is_banned) {
                    $statusBadge = '<span class="badge bg-danger">' . labels('admin_labels.banned', 'Banned') . '</span>';
                } elseif ($customer->is_active) {
                    $statusBadge = '<span class="badge bg-success">' . labels('admin_labels.active', 'Active') . '</span>';
                } else {
                    $statusBadge = '<span class="badge bg-danger">' . labels('admin_labels.deactive', 'Deactive') . '</span>';
                }

                // Row data
                $rows[] = [
                    'id'         => $customer->id,
                    'name'       => $customer->username,
                    'email'      => $customer->email,
                    'mobile'     => $customer->mobile,
                    'last_active' => $customer->last_active,
                    'baned'      => $banned,
                    'operate'    => $action,
                    'status'     => $statusBadge,
                ];
            }

            // Final response
            return response()->json([
                'total' => $total,
                'rows'  => $rows,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching customers list: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
            return response()->json([
                'total' => 0,
                'rows'  => [],
            ]);
        }
    }

    /**
     * Show the customer's details.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        // Fetch customer with related address and city
        $customer = User::with('address.city')->findOrFail($id);

        //Determine user status badge
        if ($customer->is_deleted === true) {
            $status = '<span class="badge bg-danger">' . labels('admin_labels.deleted', 'Deleted') . '</span>';
        } else  if ($customer->is_banned === true) {
            $status = '<span class="badge bg-danger">' . labels('admin_labels.banned', 'Banned') . '</span>';
        } else if ($customer->is_active === true) {
            $status = '<span class="badge bg-success">' . labels('admin_labels.active', 'Active') . '</span>';
        } else {
            $status = '<span class="badge bg-danger">' . labels('admin_labels.deactive', 'Deactive') . '</span>';
        }

        // Get all claimed offers by this user (with related offer and store info)
        $claimedOffers = OfferClaim::with(['offer', 'offer.store'])
            ->where('user_id', $id)
            ->latest()
            ->paginate(10);

        //Statistics for claimed offers
        $cliamedCount = OfferClaim::where('user_id', $id)->count();
        $redeemedCount = OfferClaim::where('user_id', $id)->where('is_redeemed', true)->count();
        $pendingCount = OfferClaim::where('user_id', $id)->where('is_redeemed', false)->count();
        $favoriteCount = Favorite::where('user_id', $id)->count();
        //Return the view with all required data
        return view('admin.pages.views.customer', compact('customer', 'claimedOffers', 'status', 'cliamedCount', 'redeemedCount', 'pendingCount', 'favoriteCount'));
    }
    /**
     * 
     */

    /**
     * Toggle the banned status of a user (ban/unban).
     *
     * This method is used to switch a user's `is_banned` flag.
     *
     * @param Request $request - The incoming request with the ban status.
     * @param int $id - ID of the user to toggle.
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleBan(Request $request, $id)
    {
        try {
            // Find the user by ID or fail if not found
            $user = User::findOrFail($id);

            // Toggle the banned status
            $user->is_banned = $user->is_banned === true ? false : true;

            //  Save the updated user state
            $user->save();

            //  Return success response with appropriate message
            return response()->json([
                'success' => true,
                'message' => $user->is_banned
                    ? __('admin_labels.user_banned_successfully')    // Message if now banned
                    : __('admin_labels.user_unbanned_successfully')  // Message if now unbanned
            ]);
        } catch (\Exception $e) {
            Log::error("Error toggling ban: " . $e->getMessage());

            //  Return failure response
            return response()->json([
                'success' => false,
                'message' => __('admin_labels.something_went_wrong')
            ]);
        }
    }
    /////////////////////////////






    public function getCategories($id = null, $limit = '', $offset = '', $sort = 'row_order', $order = 'ASC', $has_child_or_item = 'true', $slug = '', $ignore_status = '', $seller_id = '')
    {
        $level = 0;
        $store_id = getStoreId();

        if ($ignore_status == 1) {
            $where = (isset($id) && !empty($id)) ? ['id' => $id, 'store_id' => $store_id] : ['parent_id' => 0, 'store_id' => $store_id];
        } else {
            $where = (isset($id) && !empty($id)) ? ['id' => $id, 'status' => 1, 'store_id' => $store_id] : ['parent_id' => 0, 'status' => 1, 'store_id' => $store_id];
        }

        $query = Category::orderBy($sort, $order)
            ->where($where);

        if ($has_child_or_item == 'false') {
            $query->leftJoin('categories as c2', 'c2.parent_id', '=', 'id')
                ->leftJoin('products as p', 'p.category_id', '=', 'id')

                ->where(function ($query) {
                    $query->where('id', '=', DB::raw('p.category_id'))
                        ->orWhere('c2.parent_id', '=', 'id');
                })
                ->groupBy('id');
        } else {

            if (!empty($limit)) {
                $query->take($limit);
            }

            if (!empty($offset)) {
                $query->skip($offset);
            }
        }


        $categories = $query->get();

        $i = 0;
        foreach ($categories as $p_cat) {
            $categories[$i]->children = $this->subCategories($p_cat->id, $level);
            $categories[$i]->text = e($p_cat->name);
            $categories[$i]->name = ($categories[$i]->name);
            $categories[$i]->state = ['opened' => true];
            $categories[$i]->icon = "jstree-folder";
            $categories[$i]->level = $level;
            $p_cat['image'] = getMediaImageUrl($p_cat['image']);
            $p_cat['banner'] = getMediaImageUrl($p_cat['banner']);
            $i++;
        }

        return $categories;
    }

    public function subCategories($id, $level)
    {
        $level = $level + 1;
        $category = Category::find($id);
        $categories = $category->children;

        $i = 0;
        foreach ($categories as $p_cat) {
            $categories[$i]->children = $this->subCategories($p_cat->id, $level);
            $categories[$i]->text = e($p_cat->name); // Use the Laravel "e" helper for output escaping
            $categories[$i]->state = ['opened' => true];
            $categories[$i]->level = $level;
            $p_cat['image'] = getMediaImageUrl($p_cat['image']);
            $p_cat['banner'] = getMediaImageUrl($p_cat['banner']);
            $i++;
        }

        return $categories;
    }

    public function seller_register()
    {
        // $store_id = getStoreId();
        $setting = getSettings('system_settings', true);
        $setting = json_decode($setting, true);
        $logo = file_exists(public_path(config('constants.MEDIA_PATH') . $setting['logo']))
            ? asset(config('constants.MEDIA_PATH') . $setting['logo'])
            : asset(config('constants.DEFAULT_LOGO'));
        $locale = app()->getLocale();
        $categories = Category::whereNull('parent_id')->select('id', 'name_' . $locale . ' as name')->get();
        return view('seller/pages/forms/register', compact('categories', 'logo'));
    }

    public function sellerStore(StoreReqiserRequest $request)
    {
        try {
            $role_id = Role::where('name', 'seller')->value('id');
            if (User::where('email', $request->email)->where('role_id', $role_id)->exists()) {
                return redirect()->back()
                    ->with('error', __('admin_labels.email_already_exists'))
                    ->withInput();
            }
            if (User::where('mobile', $request->mobile)->where('role_id', $role_id)->exists()) {
                return redirect()->back()
                    ->with('error', __('admin_labels.phone_already_exists'))
                    ->withInput();
            }
            DB::beginTransaction();

            //  Create User
            $user = User::create([
                'username' => $request->name,
                'email' => $request->email,
                'mobile' => $request->mobile,
                'password' => Hash::make($request->password),
                'role_id' => $role_id,
                'is_active' => 0,
            ]);

            //  Upload files
            $logoPath =   upload_image($request->logo, 'stores', 'logo');
            $coverPath = $request->hasFile('cover') ?  upload_image($request->cover, 'stores', 'cover') : null;

            // Create Store
            $store = Store::create([
                'user_id' => $user->id,
                'name_ar' => $request->store_name_ar,
                'name_en' => $request->store_name_en,
                'description_ar' => $request->description_ar,
                'description_en' => $request->description_en,
                'logo' => $logoPath,
                'cover' => $coverPath,
                'is_active' => true,
            ]);

            //Attach Categories
            $store->categories()->sync($request->categories);
            Log::info("Store created", [$store]);

            DB::commit();
            return redirect()->back()
                ->with('success', __('admin_labels.registration_successful'));
            return redirect()->route('seller.login')->with('success', __('admin_labels.registration_successful'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error creating store: " . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', __('admin_labels.default_error_message') . ': ' . $e->getMessage());
        }

        // if (isset($responseData['error_message'])) {
        //     return response()->json([
        //         'message' => $responseData['error_message']
        //     ]);
        // } else {
        //     return response()->json([
        //         'message' => isset($responseData['message']) ? $responseData['message'] : $responseData['errors'],
        //         'location' => route('seller.login')
        //     ]);
        // }

    }

    public function sendOtp(Request $request)
    {
        $request->validate(['mobile' => 'required|string']);

        $otp = rand(100000, 999999);
        // Session::put('otp_' . $request->mobile, $otp);

        // // Send OTP logic here (SMS API or log)
        // // For development, you can log it:
        Log::info("OTP for {$request->mobile}: {$otp}");

        return response()->json([
            'message' => __('admin_labels.otp_sent_success')
        ]);
    }



    public function delete_selected_data(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:users,id'
        ]);

        foreach ($request->ids as $id) {
            $users = User::find($id);

            if ($users) {
                User::where('id', $id)->delete();
            }
        }
        User::destroy($request->ids);

        return response()->json(['message' => 'Selected data deleted successfully.']);
    }
}
