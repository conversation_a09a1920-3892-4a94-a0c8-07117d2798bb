<?php $__env->startSection('title'); ?>
    <?php echo e(labels('admin_labels.seller_email_notification', 'Seller Email Notifications')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="d-flex row align-items-center">
        <div class="col-md-6 col-xl-6 page-info-title">
            <h3><?php echo e(labels('admin_labels.seller_email_notification', 'Seller Email Notifications')); ?>

            </h3>
          
        </div>
        <div class="col-md-6 col-xl-6 d-flex justify-content-end" dir='ltr'>
            <nav aria-label="breadcrumb" class="float-end">
                <ol class="breadcrumb">
                    <i class='bx bx-home-smile'></i>
                    <li class="breadcrumb-item"><a
                            href="<?php echo e(route('admin.home')); ?>"><?php echo e(labels('admin_labels.home', 'Home')); ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?php echo e(labels('admin_labels.notifications', 'Notifications')); ?>

                    </li>
                </ol>
            </nav>
        </div>
    </div>
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-12 col-lg-12">
                <div class="card card-info">
                    <form class="form-horizontal submit_form" action="<?php echo e(route('email_notifications.store')); ?>" method="POST"
                        id="" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="card-body">
                            <h5 class="mb-3">
                                <?php echo e(labels('admin_labels.send_notification', 'Send Notifications')); ?>

                            </h5>
                            <div class="form-group">
                                <label for=""
                                    class="control-label mb-2 mt-2"><?php echo e(labels('admin_labels.send_to', 'Send to')); ?><span
                                        class='text-asterisks text-sm'>*</span></label>
                                <select name="send_to" id="send_seller_notification"
                                    class="form-control form-select type_event_trigger" required>
                                    <option value="all_sellers"><?php echo e(__('admin_labels.all_store')); ?></option>
                                    <option value="specific_seller"><?php echo e(__('admin_labels.specific_store')); ?></option>
                                </select>
                            </div>
                            <div class="form-group row notification-sellers d-none">
                                <label for="user_id"
                                    class="col-md-12 control-label"><?php echo e(labels('admin_labels.stores', 'Stores')); ?>

                                    <span class='text-asterisks text-sm'>*</span></label>
                                <div class="col-md-12">
                                    <input type="hidden" name="user_id" id="noti_user_id" value="">
                                    <select name="select_user_id[]" class="search_seller w-100" multiple
                                         data-placeholder="Type to search and select sellers"
                                        onload="multiselect()">
                                        <!-- Users options here -->
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="title"
                                    class="control-label mb-2 mt-2"><?php echo e(labels('admin_labels.subject', 'Subject')); ?>

                                    <span class='text-asterisks text-sm'>*</span></label>
                                <input type="text" class="form-control" name="subject" id="subject"
                                    value="">
                            </div>
                            <div class="form-group">
                                <label for="message"
                                    class="control-label mb-2 mt-2"><?php echo e(labels('admin_labels.message', 'Message')); ?>

                                    <span class='text-asterisks text-sm'>*</span></label>
                                    <textarea class="form-control addr_editor" placeholder="Message"
                                    name="message"></textarea>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="reset"
                                    class="btn mx-2 reset_button"><?php echo e(labels('admin_labels.reset', 'Reset')); ?></button>
                                <button type="submit"
                                    class="btn btn-primary submit_button"><?php echo e(labels('admin_labels.send_notification', 'Send Notification')); ?></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin/layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/pages/forms/seller_email_notification.blade.php ENDPATH**/ ?>