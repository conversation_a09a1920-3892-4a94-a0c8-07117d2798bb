

<?php $__env->startSection('title', __('admin_labels.ad_details')); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <div class="card border-0 shadow-sm rounded-3 overflow-hidden">
        <!-- Header with gradient background -->
        <div class="card-header bg-gradient py-3" style="background: linear-gradient(135deg, #6f42c1 0%, #b73fbb 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-white">
                    <i class="bx bx-purchase-tag-alt me-2"></i>
                    <?php echo e(__('admin_labels.ad_details')); ?>

                </h5>
                <span class="badge bg-white text-dark">
                    #<?php echo e($ad->id); ?>

                </span>
            </div>
        </div>

        <div class="card-body p-4">
            <!-- Main Info Section -->
            <div class="row g-4 mb-4">
                <!-- Ad Info Card -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                <i class="bx bx-info-circle me-2"></i>
                                <?php echo e(__('admin_labels.ad_info')); ?>

                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <?php if($ad->image_url): ?>
                                <img src="<?php echo e(getMediaImageUrl($ad->image_url)); ?>" class="rounded-3 border mb-3" style="max-height: 200px; width: auto;">
                                <?php endif; ?>
                            </div>

                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;"><?php echo e(__('admin_labels.name')); ?></span>
                                <div>
                                    <span class="fw-medium"><?php echo e($ad->title_en); ?></span>
                                    <?php if($ad->title_ar): ?>
                                    <span class="d-block text-muted small"><?php echo e($ad->title_ar); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;"><?php echo e(__('admin_labels.description')); ?></span>
                                <div>
                                    <span class="fw-medium"><?php echo e($ad->description_en); ?></span>
                                    <?php if($ad->description_ar): ?>
                                    <span class="d-block text-muted small"><?php echo e($ad->description_ar); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;"><?php echo e(__('admin_labels.ad_type')); ?></span>
                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                    <?php echo e($ad->advertisement_type->label()); ?>

                                </span>
                            </div>

                            <?php if($ad->link): ?>
                            <div class="d-flex">
                                <span class="text-muted flex-shrink-0" style="width: 120px;"><?php echo e(__('admin_labels.link')); ?></span>
                                <a href="<?php echo e($ad->link); ?>" target="_blank" class="text-primary text-truncate d-block"><?php echo e($ad->link); ?></a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Store Info Card -->
                <?php if($ad->storeData): ?>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                <i class="bx bx-store me-2"></i>
                                <?php echo e(__('admin_labels.store_info')); ?>

                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-start">
                                <?php if($ad->storeData->logo): ?>
                                <div class="flex-shrink-0 me-3">
                                    <img src="<?php echo e(getMediaImageUrl($ad->storeData->logo)); ?>" class="rounded-circle border" width="60" height="60" style="object-fit: cover;">
                                </div>
                                <?php endif; ?>
                                <div class="flex-grow-1">
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 100px;">ID</span>
                                        <span class="fw-medium"><?php echo e($ad->storeData->id); ?></span>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 100px;"><?php echo e(__('admin_labels.name')); ?></span>
                                        <span class="fw-medium"><?php echo e($ad->storeData->name_en); ?></span>
                                    </div>
                                    <div class="d-flex">
                                        <span class="text-muted flex-shrink-0" style="width: 100px;"><?php echo e(__('admin_labels.mobile')); ?></span>
                                        <span class="fw-medium"><?php echo e($ad->storeData->user->mobile); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Second Row Section -->
            <div class="row g-4 mb-4">
                <!-- Creator Info Card -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                <?php if($creatorUser): ?>
                                <i class="bx bx-user me-2"></i>
                                <?php echo e(__('admin_labels.created_by_user')); ?>

                                <?php else: ?>
                                <i class="bx bx-shield-quarter me-2"></i>
                                <?php echo e(__('admin_labels.created_by')); ?>

                                <?php endif; ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if($creatorUser): ?>
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 100px;">ID</span>
                                <span class="fw-medium"><?php echo e($creatorUser->id); ?></span>
                            </div>
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 100px;"><?php echo e(__('admin_labels.name')); ?></span>
                                <span class="fw-medium"><?php echo e($creatorUser->username); ?></span>
                            </div>
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 100px;"><?php echo e(__('admin_labels.email')); ?></span>
                                <span class="fw-medium"><?php echo e($creatorUser->email); ?></span>
                            </div>
                            <div class="d-flex">
                                <span class="text-muted flex-shrink-0" style="width: 100px;"><?php echo e(__('admin_labels.phone')); ?></span>
                                <span class="fw-medium"><?php echo e($creatorUser->mobile ?? '-'); ?></span>
                            </div>
                            <?php else: ?>
                            <span class="badge bg-primary bg-opacity-10 text-primary py-2 px-3">
                                <?php echo e(__('admin_labels.created_by_admin')); ?>

                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Package Info Card -->
                <?php if($ad->package): ?>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                <i class="bx bx-package me-2"></i>
                                <?php echo e(__('admin_labels.package_info')); ?>

                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;"><?php echo e(__('admin_labels.package')); ?></span>
                                <span class="fw-medium"><?php echo e($ad->package->title); ?></span>
                            </div>
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;"><?php echo e(__('admin_labels.price')); ?></span>
                                <span class="fw-medium"><?php echo e($ad->package->price); ?></span>
                            </div>
                            <div class="d-flex mb-2">
                                <span class="text-muted flex-shrink-0" style="width: 120px;"><?php echo e(__('admin_labels.type')); ?></span>
                                <span class="fw-medium"><?php echo e($ad->package->type->label()); ?></span>
                            </div>
                            <div class="d-flex">
                                <span class="text-muted flex-shrink-0" style="width: 120px;"><?php echo e(__('admin_labels.duration')); ?></span>
                                <span class="fw-medium"><?php echo e($ad->package->duration_in_days); ?> <?php echo e(__('admin_labels.days')); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Offer Info Section -->
            <?php if($ad->advertisement_type->value === 'OFFER' && $ad->offer): ?>
            <div class="row g-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                <i class="bx bx-gift me-2"></i>
                                <?php echo e(__('admin_labels.offer_info')); ?>

                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 150px;">ID</span>
                                        <span class="fw-medium"><?php echo e($ad->offer->id); ?></span>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 150px;"><?php echo e(__('admin_labels.name_en')); ?></span>
                                        <span class="fw-medium"><?php echo e($ad->offer->title_en); ?></span>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 150px;"><?php echo e(__('admin_labels.name_ar')); ?></span>
                                        <span class="fw-medium"><?php echo e($ad->offer->title_ar); ?></span>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 150px;"><?php echo e(__('admin_labels.price')); ?></span>
                                        <span class="fw-medium"><?php echo e($ad->offer->price); ?></span>
                                    </div>
                                    <div class="d-flex">
                                        <span class="text-muted flex-shrink-0" style="width: 150px;"><?php echo e(__('admin_labels.discounted_price')); ?></span>
                                        <span class="fw-medium"><?php echo e($ad->offer->discounted_price); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex mb-2">
                                        <span class="text-muted flex-shrink-0" style="width: 150px;"><?php echo e(__('admin_labels.expire_date')); ?></span>
                                        <span class="fw-medium"><?php echo e($ad->offer->expire_date); ?></span>
                                    </div>
                                    <?php if($ad->offer->images->first()): ?>
                                    <div class="mt-3">
                                        <img src="<?php echo e(getMediaImageUrl($ad->offer->images->first()->image_url)); ?>" class="rounded border" style="max-height: 150px;">
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Footer with Action Buttons -->
        <div class="card-footer bg-light d-flex justify-content-end gap-3 py-3">
            <?php if($ad->is_approved === null): ?>
            <form method="POST" action="<?php echo e(route('admin.ads.approve', $ad->id)); ?>" class="mb-0">
                <?php echo csrf_field(); ?>
                <button type="submit" class="btn btn-success btn-sm d-inline-flex align-items-center" id="approveBtn">
                    <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true" id="approveSpinner"></span>
                    <?php echo e(__('admin_labels.approve')); ?>

                </button>

            </form>

            <form method="POST" action="<?php echo e(route('admin.ads.reject', $ad->id)); ?>" class="mb-0">
                <?php echo csrf_field(); ?>
                <button type="submit" class="btn btn-danger btn-sm d-inline-flex align-items-center" id="rejectBtn">
                    <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true" id="rejectSpinner"></span>
                    <?php echo e(__('admin_labels.reject')); ?>

                </button>

            </form>
            <?php elseif($ad->is_approved): ?>
            <span class="badge bg-success px-3 py-2 d-flex align-items-center">
                <i class="bx bx-check-circle me-1"></i> <?php echo e(__('admin_labels.approved')); ?>

            </span>
            <?php else: ?>
            <span class="badge bg-danger px-3 py-2 d-flex align-items-center">
                <i class="bx bx-x-circle me-1"></i> <?php echo e(__('admin_labels.rejected')); ?>

            </span>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
    }

    .badge {
        padding: 0.35em 0.65em;
        font-weight: 500;
    }

    .rounded-2 {
        border-radius: 0.5rem;
    }

    .rounded-3 {
        border-radius: 0.75rem;
    }
</style>
<?php $__env->stopPush(); ?>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const approveBtn = document.getElementById('approveBtn');
        const approveSpinner = document.getElementById('approveSpinner');
        const rejectBtn = document.getElementById('rejectBtn');
        const rejectSpinner = document.getElementById('rejectSpinner');

        if (approveBtn) {
            approveBtn.addEventListener('click', function() {
                approveSpinner.classList.remove('d-none');
            });
        }

        if (rejectBtn) {
            rejectBtn.addEventListener('click', function() {
                rejectSpinner.classList.remove('d-none');
            });
        }
    });
</script>
<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/pages/views/requested_ad.blade.php ENDPATH**/ ?>