<?php

namespace App\Models\awfarly;

use App\Enums\AdvertismentPackageType;
use App\Enums\AdvertismentStatus;
use App\Enums\AdvertismentType;
use App\Enums\UserRole;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Advertisment extends Model
{
    protected $table = 'advertisments';
    protected $fillable = [
        'title',
        'description',
        'image_url',
        'created_by_type',
        'created_by_id',
        'package_id',
        'status',
        'start_date',
        'end_date',
        'advertisement_type',
        'offer_id',
        'store_id',
        'package_type',
        'is_paid',
        'link'
        
    ];

    /**
     *  the attributes that should be cast.
     *
     * @return array<string, string>
     */

    protected $casts = [
        'id' => 'integer',
        'start_date' => 'datetime',
        'end_date'   => 'datetime',
        'advertisement_type' => AdvertismentType::class,
        'status' => AdvertismentStatus::class,
        'package_type'=>AdvertismentPackageType::class,
        'created_by_type'=>UserRole::class,
    ];


    // Relationships

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo(AdvertismentPackage::class, 'package_id');
    }

    public function offer(): BelongsTo
    {
        return $this->belongsTo(Offer::class,'offer_id');
    }
    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id');
    }
}
