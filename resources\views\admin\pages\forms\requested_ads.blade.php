@extends('admin.layout')

@section('title', __('admin_labels.requested_ads'))

@section('content')
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="bx bx-check-circle me-2"></i> {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif
@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="bx bx-error-circle me-2"></i> {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

<div class="container py-4">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom-0 py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="bx bx-list-ul me-2"></i>
                    {{ __('admin_labels.requested_ads') }}
                </h5>

            </div>
        </div>

        <div class="card-body">
            <div class="row g-4">
                @forelse($ads as $ad)
                <div class="col-12">
                    <div class="card border-0 shadow-sm hover-lift">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-start gap-3">
                                <!-- Image Column -->
                                @if($ad->image)
                                <div class="flex-shrink-0">
                                    <img src="{{ getMediaImageUrl($ad->image_url) }}"
                                        class="rounded-2 border"
                                        style="width: 120px; height: 90px; object-fit: cover;">
                                </div>
                                @endif

                                <!-- Content Column -->
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1 fw-semibold">{{ $ad->title_ar}}</h6>
                                            <p class="small text-muted mb-2">{{ $ad->description_ar ?? $ad->description_ar }}</p>

                                            <div class="d-flex flex-wrap gap-3">
                                                <span class="badge bg-primary bg-opacity-10 text">
                                                    {{ $ad->advertisement_type->label() }}
                                                </span>
                                                @if($ad->package)
                                                <span class="badge bg-primary bg-opacity-10 text-black">
                                                    {{ $ad->package->price }} د.ل
                                                </span>
                                                <span class="badge bg-primary bg-opacity-10 text-black">
                                                    {{ $ad->package->title }}
                                                </span>
                                                @endif
                                            </div>
                                        </div>

                                        <div class="text-end">
                                            <small class="text-muted d-block">{{ __('admin_labels.store_name') }}</small>
                                            <span class="fw-medium">{{ $ad->storeData?->name_ar ?? '-' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Footer with Dates and Actions -->
                            <div class="d-flex justify-content-between align-items-center mt-3 pt-2 border-top">
                                <div class="small text-muted">
                                    <span class="me-3"><i class="bx bx-calendar me-1"></i> {{ $ad->start_date }}</span>
                                    <span><i class="bx bx-calendar-event me-1"></i> {{ $ad->end_date }}</span>
                                </div>

                                <div class="d-flex gap-2">
                                    <a href="{{ route('admin.requested.show', $ad->id) }}"
                                        class="btn btn-sm btn-outline-primary px-3 rounded-2">
                                        <i class="bx bx-show me-1"></i> {{ __('admin_labels.view') }}
                                    </a>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bx bx-package text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">{{ __('admin_labels.no_requested_ads') }}</h5>
                        <p class="text-muted">{{ __('admin_labels.no_ads_description') }}</p>
                    </div>
                </div>
                @endforelse
            </div>

            <!-- Pagination -->
            @if($ads->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $ads->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .hover-lift {
        transition: all 0.25s ease;
    }

    .hover-lift:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
    }

    .badge {
        font-weight: 500;
        padding: 0.35em 0.65em;
    }
</style>

@endpush
