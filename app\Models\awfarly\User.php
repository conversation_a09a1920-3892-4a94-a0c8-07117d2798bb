<?php

namespace App\Models\awfarly;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Enums\FavoritesType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;

use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;



class User extends Authenticatable implements HasMedia
{
    use InteractsWithMedia;

    use HasApiTokens, HasFactory, Notifiable, HasPermissions, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */


    protected $fillable = [
        'role_id',
        'name',
        'email',
        'mobile',
        'password',
        'fcm_id',
        'ip_address',
        'is_active',
        'subscription_expires_at',
        'is_deleted',
        'is_banned'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];


    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'role_id' => 'integer',
        'is_active' => 'boolean',
        'is_deleted' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'subscription_expires_at' => 'datetime',
        'is_banned' => 'boolean',
    ];

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }
    public function address(): HasMany
    {
        return $this->hasMany(Address::class);
    }

    public function stores()
    {
        return $this->belongsTo(Store::class);
    }

    public function offers()
    {
        return $this->hasMany(Offer::class, 'store_id');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    public function ratings()
    {
        return $this->hasMany(Rating::class);
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    public function reports()
    {
        return $this->hasMany(ReportIssuse::class, 'reported_by');
    }

    public function claims()
    {
        return $this->hasMany(OfferClaim::class);
    }

    public function asvertisment()
    {
        return $this->hasMany(Advertisment::class);
    }
    public function favoriteOffers()
    {
        return $this->favorites()->where('type', FavoritesType::OFFER)->orderBy('created_at', 'desc');
    }

    public function favoriteCategories()
    {
        return $this->favorites()->where('type', FavoritesType::CATEGORY)->orderBy('created_at', 'desc');
    }

    public function favoriteStores()
    {
        return $this->favorites()->where('type', FavoritesType::STORE)->orderBy('created_at', 'desc');
    }
    public function storeUser(): HasMany
    {
        return $this->hasMany(StoreUser::class);
    }
}

// namespace App\Models;

// // use Illuminate\Contracts\Auth\MustVerifyEmail;
// use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Relations\BelongsTo;
// use Illuminate\Database\Eloquent\Relations\HasMany;
// use Illuminate\Foundation\Auth\User as Authenticatable;
// use Illuminate\Notifications\Notifiable;
// use Laravel\Sanctum\HasApiTokens;

// class User extends Authenticatable
// {
//     /** @use HasFactory<\Database\Factories\UserFactory> */
//     use  HasApiTokens, HasFactory, Notifiable;

//     protected $fillable = [
//         'name',
//         'phone',
//         'email',
//         'role_id',
//         'password',
//         'city_id'
//     ];

//     protected $hidden = [
//         'password',
//         'remember_token',
//         'role_id',
//     ];

//     /**
//      *  the attributes that should be cast.
//      *
//      * @return array<string, string>
//      */

//     protected $casts = [
//         'id' => 'integer',
//         'phone' => 'integer',
//         'is_active' => 'boolean',
//         'subsription_requier' => 'boolean',

//     ];
//     public function role() : BelongsTo
//     {
//         return $this->belongsTo(Role::class);
//     }
//     public function city(): BelongsTo
//     {
//         return $this->belongsTo(City::class);
//     }

//     public function stores()
//     {
//         return $this->belongsTo(Store::class);
//     }

//     public function offers()
//     {
//         return $this->hasMany(Offer::class, 'store_id');
//     }

//     public function transactions()
//     {
//         return $this->hasMany(Transaction::class);
//     }

//     public function favorites()
//     {
//         return $this->hasMany(Favorite::class);
//     }

//     public function ratings()
//     {
//         return $this->hasMany(Rating::class);
//     }

//     public function notifications()
//     {
//         return $this->hasMany(Notification::class);
//     }

//     public function reports()
//     {
//         return $this->hasMany(ReportOffer::class, 'reported_by');
//     }

//     public function claims()
//     {
//         return $this->hasMany(OfferClaim::class);
//     }
// }
