<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UserFcmToken extends Model
{
    protected $fillable = [
        'user_id',
        'fcm_token',
    ];


    // Relationships

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
