 <!-- Sidebar -->
 <nav class="navbar-vertical navbar bg-white" <?php echo e(session()->get('is_rtl') == 1 ? 'dir=rtl' : ''); ?>>
     <div class="nav-scroller bg-white">
         <?php
             $user = auth()->user();
             use Chatify\ChatifyMessenger;

             $setting = getSettings('system_settings', true);
             $setting = json_decode($setting, true);

             $sms_gateway_settings = getSettings('sms_gateway_settings');
             $messenger = new ChatifyMessenger();
             $unread = $messenger->totalUnseenMessages();
         ?>
         <input type="hidden" id="sms_gateway_data" value='<?php echo e($sms_gateway_settings); ?>' />
         <div class="sidenav-header">
             <i class="fas fa-times p-3 cursor-pointer text-secondary opacity-5 position-absolute end-0 top-0 d-none d-xl-none"
                 aria-hidden="true" id="iconSidenav"></i>
             <a class="navbar-brand m-0" href="<?php echo e(route('admin.home')); ?>" target="">
                 <?php
                     $store_logo =
                         !empty($setting['logo']) &&
                         file_exists(public_path(config('constants.MEDIA_PATH') . $setting['logo']))
                             ? getMediaImageUrl($setting['logo'])
                             : asset('assets/img/default_full_logo.png');
                 ?>
                 <img src="<?php echo e($store_logo); ?>" class="navbar-brand-img" alt="main_logo">
             </a>
         </div>
         <hr class="horizontal dark mt-0">

         <!-- code for menu search -->

         <div class="ps-2 pe-2">
             <!-- Search Bar -->
             <input type="text" class="form-control menuSearch" placeholder="Search Menu...">
         </div>


         <ul class="navbar-nav" id="menuList">
             <li class="sidebar-title ms-3"><i class='bx bx-tachometer'></i>
                 <?php echo e(labels('admin_labels.dashboard', 'Dashboard')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/home') || Request::is('admin/home/<USER>') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.home')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.dashboard', 'Dashboard')); ?></span>
                 </a>
             </li>

             <li class="sidebar-title ms-3"><i class='bx bx-store-alt'></i>
                 <?php echo e(labels('admin_labels.stores', 'Stores')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/store') || Request::is('admin/store') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.stores.index')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.add_store', 'Add Store')); ?></span>
                 </a>
             </li>
             <?php if($user_role == 'super_admin' || $user->hasPermissionTo('view store')): ?>
                 <li class="nav-item ms-3">
                     <a class="nav-link <?php echo e(Request::is('admin/stores/manage_store') || Request::is('admin/stores/manage_store*') ? 'active' : ''); ?>"
                         href="<?php echo e(route('admin.stores.manage_store')); ?>">
                         <span
                             class="nav-link-text ms-1"><?php echo e(labels('admin_labels.manage_stores', 'Manage Stores')); ?></span>
                     </a>
                 </li>
             <?php endif; ?>
             <?php if($user_role == 'super_admin' || $user->hasPermissionTo('view store')): ?>
                 <li class="nav-item ms-3">
                     <a class="nav-link <?php echo e(Request::is('admin/stores/requested-stores') || Request::is('admin/stores/requested-stores*') ? 'active' : ''); ?>"
                         href="<?php echo e(route('admin.stores.manage_requested_stores')); ?>">
                         <span
                             class="nav-link-text ms-1"><?php echo e(labels('admin_labels.manage_requested_stores', 'Manage Requested Stores')); ?></span>
                     </a>
                 </li>
             <?php endif; ?>
             <?php if($user_role == 'super_admin' || $user->hasPermissionTo('view seller_wallet_transaction')): ?>
                 <li class="nav-item ms-3">
                     <a class="nav-link <?php echo e(Request::is('admin/stores/transaction') || Request::is('admin/stores/transaction/*') ? 'active' : ''); ?>"
                         href="<?php echo e(route('admin.stores.storeTransactions')); ?>">
                         <span
                             class="nav-link-text ms-1"><?php echo e(labels('admin_labels.wallet_transactions', 'Wallet Transaction')); ?></span>
                     </a>
                 </li>
             <?php endif; ?>
             <?php if($user_role == 'super_admin' || $user->hasPermissionTo('view orders')): ?>
                 <li class="sidebar-title ms-3"><i class='bx bx-card'></i>
                     <?php echo e(labels('admin_labels.manage', 'Manage')); ?>

                 </li>
                 <li class="nav-item ms-3">
                     <a data-bs-toggle="collapse" href="#order_dropdown"
                         class="nav-link collapsed <?php echo e(Request::is('admin/orders') || Request::is('admin/orders*') || Request::is('admin/order_items') ? 'active' : ''); ?> <?php echo e(Request::is('admin/orders') || Request::is('admin/orders*') || Request::is('admin/order_items') ? '' : 'collapsed'); ?>"
                         aria-controls="order_dropdown" role="button" aria-expanded="false">

                         <span
                             class="nav-link-text ms-1"><?php echo e(__('admin_labels.manage_offers')); ?></span>
                         <i class="fas fa-angle-down"></i>
                     </a>
                     <div class="collapse <?php echo e(Request::is('admin/orders') || Request::is('admin/orders*') || Request::is('admin/order_items') ? 'show' : ''); ?>"
                         id="order_dropdown">
                         <ul class="nav">
                             <li class="nav-item <?php echo e(Request::is('admin/orders') ? 'active' : ''); ?>">
                                 <a class="nav-link " href="<?php echo e(route('admin.orders.index')); ?>">
                                     <span
                                         class="nav-link-text ms-1"><?php echo e(__('admin_labels.offers')); ?></span>
                                 </a>
                             </li>
                             <li
                                 class="nav-item <?php echo e(Request::is('admin/reported_offers') || Request::is('admin/reported_offers*') ? 'active' : ''); ?>">
                                 <a class="nav-link " href="<?php echo e(route('admin.reported_offers.index')); ?>">
                                     <span
                                         class="nav-link-text ms-1"><?php echo e(__('admin_labels.offer_reports')); ?></span>
                                 </a>
                             </li>
                             <li class="nav-item <?php echo e(Request::is('admin/orders/order_tracking') ? 'active' : ''); ?>">
                                 <a class="nav-link " href="<?php echo e(route('admin.orders.order_tracking')); ?>">
                                     <span
                                         class="nav-link-text ms-1"><?php echo e(__('admin_labels.offer_codes')); ?></span>
                                 </a>
                             </li>
                         </ul>
                     </div>
                 </li>
             <?php endif; ?>
             <li class="nav-item ms-3">
                 <a data-bs-toggle="collapse" href="#category_dropdown"
                     class="nav-link <?php echo e(Request::is('admin/categories') || Request::is('admin/categories/*') ? '' : 'collapsed'); ?> <?php echo e(Request::is('admin/categories') || Request::is('admin/categories/*') ? 'active' : ''); ?>"
                     aria-controls="category_dropdown" role="button" aria-expanded="false">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.categories', 'Categories')); ?></span>
                     <i class="fas fa-angle-down"></i>
                 </a>
                 <div class="collapse <?php echo e(Request::is('admin/categories') || Request::is('admin/categories/*') ? 'show' : ''); ?>"
                     id="category_dropdown">
                     <ul class="nav">
                         <li
                             class="nav-item <?php echo e(Request::is('admin/categories') || Request::is('admin/categories') ? 'active' : ''); ?>">
                             <a class="nav-link" href="<?php echo e(route('categories.index')); ?>">
                                 <span
                                     class="nav-link-text ms-1"><?php echo e(labels('admin_labels.categories', 'Categories')); ?></span>
                             </a>
                         </li>
                    
                         <li
                             class="nav-item <?php echo e(Request::is('admin/categories/category_slider') || Request::is('admin/categories/category_slider/*') ? 'active' : ''); ?>">
                             <a class="nav-link " href="<?php echo e(route('category_slider.index')); ?>">
                                 <span
                                     class="nav-link-text ms-1"><?php echo e(labels('admin_labels.categories_sliders', 'Categories Sliders')); ?></span>
                             </a>
                         </li>
                     </ul>
                 </div>
             </li>
            
          

             <li class="sidebar-title ms-3"><i class='bx bx-card'></i>
                 <?php echo e(labels('admin_labels.blogs', 'Blogs')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/blogs') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.blogs.index')); ?>">
                     <span
                         class="nav-link-text ms-1"><?php echo e(labels('admin_labels.blog_categories', 'Blog Categories')); ?></span>
                 </a>
             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/manage_blogs') || Request::is('admin/manage_blogs/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('manage_blogs.index')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.create_blog', 'Create Blog')); ?></span>
                 </a>
             </li>
             
            


             <li class="sidebar-title ms-3"><i class='bx bx-store-alt'></i>
                 <?php echo e(labels('admin_labels.advertisments', 'Advertisements')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/ads/requested') || Request::is('admin/ads/requested/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.ads.requested')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.requested_ads', 'Requested Advertisements')); ?></span>
                 </a>
             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/ads') ? 'active' : ''); ?>"
                     href="<?php echo e(route('ads.index')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.advertisments', 'Advertisements')); ?></span>
                 </a>
             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/sliders') || Request::is('admin/sliders/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.sliders.index')); ?>">
                     <span
                         class="nav-link-text ms-1"><?php echo e(labels('admin_labels.sliders', 'Sliders')); ?></span>
                 </a>
             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/premuim_ads') || Request::is('admin/premuim_ads/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.premuim.index')); ?>">
                     <span
                         class="nav-link-text ms-1"><?php echo e(labels('admin_labels.premuim_ads', 'Premuim Ads')); ?></span>
                 </a>
             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/spotlight') || Request::is('admin/spotlight/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('spotlight.index')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.spotlight_ads', 'Spotlight')); ?></span>
                 </a>
             </li>


             <li class="sidebar-title ms-3"><i class='bx bx-support'></i>
                 <?php echo e(labels('admin_labels.support_tickets', 'Support Tickets')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/tickets/ticket_types') || Request::is('admin/tickets/ticket_types*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('ticket_types.index')); ?>">
                     <span
                         class="nav-link-text ms-1"><?php echo e(labels('admin_labels.ticket_types', 'Ticket Types')); ?></span>
                 </a>
             </li>
             <?php if($user_role == 'super_admin' || $logged_in_user->hasPermissionTo('view tickets')): ?>
                 <li class="nav-item ms-3">
                     <a class="nav-link <?php echo e(Request::is('admin/tickets') ? 'active' : ''); ?>"
                         href="<?php echo e(route('admin.tickets.viewTickets')); ?>">
                         <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.tickets', 'Tickets')); ?></span>
                     </a>
                 </li>
             <?php endif; ?>

             <li class="sidebar-title ms-3"><i class='bx bx-chat'></i>
                 <?php echo e(labels('admin_labels.featured_section', 'Featured Section')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/feature_section') ? 'active' : ''); ?>"
                     href="<?php echo e(route('feature_section.index')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.featured', 'Featured')); ?></span>
                 </a>
             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/feature_section/section_order') ? 'active' : ''); ?>"
                     href="<?php echo e(route('feature_section.section_order')); ?>">
                     <span
                         class="nav-link-text ms-1"><?php echo e(labels('admin_labels.sections_order', 'Sections Order')); ?></span>
                 </a>
             </li>

             <li class="sidebar-title ms-3"><i class='bx bx-user'></i>
                 <?php echo e(labels('admin_labels.customers', 'Customers')); ?>

             </li>
             <?php if($user_role == 'super_admin' || $logged_in_user->hasPermissionTo('view customers')): ?>
                 <li class="nav-item ms-3">
                     <a class="nav-link <?php echo e(Request::is('admin/customers')|| Request::is('admin/customers/*')|| Request::is('customers/*') ? 'active' : ''); ?>"
                         href="<?php echo e(route('admin.customers')); ?>">
                         <span
                             class="nav-link-text ms-1"><?php echo e(labels('admin_labels.view_customers', 'View Customers')); ?></span>
                     </a>
                 </li>
             <?php endif; ?>
            
          
           
             <li class="sidebar-title ms-3"><i class='bx bx-chat'></i><?php echo e(labels('admin_labels.faqs', 'FAQs')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/faq') || Request::is('admin/faq/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('faqs.index')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.faqs', 'FAQs')); ?></span>
                 </a>
             </li>

             <li class="sidebar-title ms-3"><i class='bx bx-send'></i>
                 <?php echo e(labels('admin_labels.send_notification', 'Send Notification')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/send_notification') || Request::is('admin/send_notification/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('notifications.index')); ?>">
                     <span
                         class="nav-link-text ms-1"><?php echo e(labels('admin_labels.notification', 'Notification')); ?></span>
                 </a>
             </li>
            
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/seller_email_notification') || Request::is('admin/seller_email_notification/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('seller_email_notifications.index')); ?>">
                     <span
                         class="nav-link-text ms-1"><?php echo e(labels('admin_labels.seller_email_notification', 'Seller Email Notification')); ?></span>
                 </a>
             </li>

             

             <li class="sidebar-title ms-3"><i
                     class='bx bx-map'></i><?php echo e(labels('admin_labels.location_management', 'Location Management')); ?>

             </li>
             
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/area/city') || Request::is('admin/area/city') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.display_city')); ?>">
                     <span class="sidenav-normal"><?php echo e(labels('admin_labels.city', 'City')); ?></span>
                 </a>
             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/pickup_location') || Request::is('admin/pickup_location') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.pickup_location.index')); ?>">
                     <span
                         class="sidenav-normal"><?php echo e(labels('admin_labels.pickup_locations', 'Pickup Locations')); ?></span>
                 </a>
             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/zones') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.zones.index')); ?>">
                     <span class="sidenav-normal"><?php echo e(labels('admin_labels.zones', 'Zones')); ?></span>
                 </a>
             </li>
             <li class="nav-item ms-3">
                <a class="nav-link <?php echo e(Request::is('admin/area/location_bulk_upload') || Request::is('admin/area/location_bulk_upload/*') ? 'active' : ''); ?>"
                    href="<?php echo e(route('admin.location_bulk_upload.index')); ?>">
                    <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.bulk_upload', 'Bulk Upload')); ?></span>
                </a>
            </li>
             <li class="sidebar-title ms-3"><i class='bx bx-cog'></i>
                 <?php echo e(labels('admin_labels.system_settings', 'System Settings')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/settings') ? 'active' : ''); ?>"
                     href="<?php echo e(route('settings.index')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.settings', 'Settings')); ?></span>
                 </a>
             </li>

            <li class="sidebar-title ms-3"><i class='bx bx-globe'></i>
                <?php echo e(labels('admin_labels.web_settings', 'Web Settings')); ?>

            </li>

            <li class="nav-item ms-3">
                <a data-bs-toggle="collapse" href="#web_setting_dropdown"
                    class="nav-link <?php echo e(Request::is('admin/web_settings*') ? '' : 'collapsed'); ?>  <?php echo e(Request::is('admin/web_settings*') ? 'active' : ''); ?>"
                    aria-controls="web_setting_dropdown" role="button" aria-expanded="false">

                    <span
                        class="nav-link-text ms-1"><?php echo e(labels('admin_labels.web_settings', 'Web Settings')); ?></span>
                    <i class="fas fa-angle-down"></i>
                </a>
                <div class="collapse <?php echo e(Request::is('admin/web_settings*') ? 'show' : ''); ?>"
                    id="web_setting_dropdown">
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(Request::is('admin/web_settings/general_settings') || Request::is('admin/web_settings/general_settings*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('general_settings')); ?>">
                                <span
                                    class="nav-link-text ms-1"><?php echo e(labels('admin_labels.general_settings', 'General Settings')); ?></span>
                            </a>
                        </li>
                    </ul>
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(Request::is('admin/web_settings/pwa_settings') || Request::is('admin/web_settings/pwa_settings*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('pwa_settings')); ?>">
                                <span
                                    class="nav-link-text ms-1"><?php echo e(labels('admin_labels.general_settings', 'PWA Settings')); ?></span>
                            </a>
                        </li>
                    </ul>
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(Request::is('admin/web_settings/firebase') || Request::is('admin/web_settings/firebase*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('firebase')); ?>">
                                <span
                                    class="nav-link-text ms-1"><?php echo e(labels('admin_labels.firebase', 'Firebase')); ?></span>
                            </a>
                        </li>
                    </ul>
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(Request::is('admin/web_settings/theme') || Request::is('admin/web_settings/theme*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('theme')); ?>">
                                <span
                                    class="nav-link-text ms-1"><?php echo e(labels('admin_labels.themes', 'Themes')); ?></span>
                            </a>
                        </li>
                    </ul>
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(Request::is('admin/web_settings/language') || Request::is('admin/web_settings/language/*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('web_language')); ?>">
                                <span
                                    class="nav-link-text ms-1"><?php echo e(labels('admin_labels.languages', 'Languages')); ?></span>
                            </a>
                        </li>
                    </ul>

                </div>
            </li>

             <li class="sidebar-title ms-3"><i
                     class='bx bx-group'></i><?php echo e(labels('admin_labels.system_users', 'System Users')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/system_users') || Request::is('admin/system_users/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.system_users.index')); ?>">
                     <span
                         class="nav-link-text ms-1"><?php echo e(labels('admin_labels.system_users', 'System Users')); ?></span>
                 </a>
             </li>
             <?php if($user_role == 'super_admin' || $logged_in_user->hasPermissionTo('view system_user')): ?>
                 <li class="nav-item ms-3">
                     <a class="nav-link <?php echo e(Request::is('admin/manage_system_users') || Request::is('admin/manage_system_users/*') ? 'active' : ''); ?>"
                         href="<?php echo e(route('admin.manage_system_users')); ?>">
                         <span
                             class="nav-link-text ms-1"><?php echo e(labels('admin_labels.manage_system_users', 'Manage System Users')); ?></span>
                     </a>
                 </li>
             <?php endif; ?>

             <li class="sidebar-title ms-3"><i class='bx bx-text'></i>
                 <?php echo e(labels('admin_labels.language_settings', 'Language Settings')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/settings/language') || Request::is('admin/settings/language/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('language.index')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.language', 'Language')); ?></span>
                 </a>
             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/settings/manage_language') || Request::is('admin/settings/manage_language/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('manage_language.index')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.manage_language', 'Manage Language')); ?></span>
                 </a>
             </li>
             <li class="sidebar-title ms-3"><i class='bx bx-text'></i>
                 <?php echo e(labels('admin_labels.reports', 'Reports')); ?>

             </li>
             <li class="nav-item ms-3">
                 <a class="nav-link <?php echo e(Request::is('admin/reports/') || Request::is('admin/settings/sales_reports/*') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.sales_reports.index')); ?>">
                     <span class="nav-link-text ms-1"><?php echo e(labels('admin_labels.sales_reports', 'Sales Reports')); ?></span>
                 </a>
             </li>
         </ul>
     </div>
 </nav>
<?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/components/admin/side-bar.blade.php ENDPATH**/ ?>