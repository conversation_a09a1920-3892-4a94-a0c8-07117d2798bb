<?php

namespace App\Models\awfarly;

use App\Enums\NotificationSendTo;
use App\Enums\NotificationType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    protected $fillable = [
        'title',
        'message',
        'type',
        'type_id',
        'send_to',
        'image',
        'link',
    ];



    /**
     *  the attributes that should be cast.
     *
     * @return array<string, string>
     */

    protected $casts = [
        'id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'send_to' => NotificationSendTo::class,
        'type' => NotificationType::class,
    ];
    // Relationships
    public function users()
    {
        return $this->belongsToMany(User::class, 'notification_user', 'notification_id', 'user_id')
            ->withTimestamps()
            ->withPivot('is_read');
    }
}
