<?php $__env->startSection('title'); ?>
    <?php echo e(labels('admin_labels.customers', 'Customers')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="d-flex row align-items-center">
        <div class="col-md-6 col-xl-6 page-info-title">
            <h3><?php echo e(labels('admin_labels.customers', 'Customers')); ?>

            </h3>
            <p class="sub_title">
                <?php echo e(labels('admin_labels.optimize_and_manage_customers', 'Optimize and Manage Customers')); ?>

            </p>
        </div>
        <div class="col-md-6 col-xl-6 d-flex justify-content-end " dir='ltr'>
            <nav aria-label="breadcrumb" class="float-end">
                <ol class="breadcrumb">
                    <i class='bx bx-home-smile'></i>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.home')); ?>"><?php echo e(labels('admin_labels.home', 'Home')); ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?php echo e(labels('admin_labels.customers', 'Customers')); ?>

                    </li>
                </ol>
            </nav>
        </div>
    </div>
    <section class="overview-data">
    <div class="row mb-5">
        <div class="col-md-12">
            <div class="heading">
                <h4><?php echo e(__('admin_labels.customers_overview')); ?></h4>
            </div>
        </div>
    </div>
    <div class="row g-4">
        <!-- Total Customers -->
        <div class="col-md-3 col-sm-6">
            <div class="card border-0 shadow-sm h-100 hover-lift" style="border-radius: 12px;">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">

                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1"><?php echo e(__('admin_labels.total_customers')); ?></h6>
                            <h4 class="mb-0 fw-bold"><?php echo e($allCustomers); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Customer -->
        <div class="col-md-3 col-sm-6">
            <div class="card border-0 shadow-sm h-100 hover-lift" style="border-radius: 12px;">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">

                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1"><?php echo e(__('admin_labels.active_customers')); ?></h6>
                            <h4 class="mb-0 fw-bold"><?php echo e($activeCustomers); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Banned Customer -->
        <div class="col-md-3 col-sm-6">
            <div class="card border-0 shadow-sm h-100 hover-lift" style="border-radius: 12px;">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">

                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1"><?php echo e(__('admin_labels.ban')); ?></h6>
                            <h4 class="mb-0 fw-bold"><?php echo e($bannedCustomers); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- deleted Customer -->
        <div class="col-md-3 col-sm-6">
            <div class="card border-0 shadow-sm h-100 hover-lift" style="border-radius: 12px;">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">

                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1"><?php echo e(__('admin_labels.deleted')); ?></h6>
                            <h4 class="mb-0 fw-bold"><?php echo e($deletedCustomers); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

    
    <div class="col-md-12 mt-4 <?php echo e($user_role== 'super_admin' || $logged_in_user->hasPermissionTo('view customers') ? '' : 'd-none'); ?>">
        <section class="overview-data">
            <div class="card content-area p-4 ">
                <div class="row align-items-center d-flex heading mb-5">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-6">
                                <h4> <?php echo e(labels('admin_labels.manage_customers', 'Manage Customers')); ?>

                                </h4>
                            </div>

                                <div class="col-md-6 d-flex justify-content-end ">
                                    <div class="input-group me-2 search-input-grp " dir='ltr'>
                                        <span class="search-icon"><i class='bx bx-search-alt'></i></span>
                                        <input type="text" data-table="admin_customer_table"
                                            class="form-control searchInput" placeholder="<?php echo e(labels('admin_labels.search', 'Search')); ?>...">
                                        <span class="input-group-text"><?php echo e(labels('admin_labels.search', 'Search')); ?></span>
                                    </div>
                                    <a class="btn me-2" id="tableFilter" data-bs-toggle="offcanvas"
                                        data-bs-target="#columnFilterOffcanvas" data-table="admin_customer_table"
                                        StatusFilter='true'><i class='bx bx-filter-alt'></i></a>
                                    <a class="btn me-2" id="tableRefresh" data-table="admin_customer_table"><i
                                            class='bx bx-refresh'></i></a>
                                    <div class="dropdown">
                                        <a class="btn dropdown-toggle export-btn" type="button" id="exportOptionsDropdown"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class='bx bx-download'></i>
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="exportOptionsDropdown">
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_customer_table','csv')">CSV</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_customer_table','json')">JSON</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_customer_table','sql')">SQL</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_customer_table','excel')">Excel</button></li>
                                        </ul>
                                    </div>
                                </div>
                        </div>
                    </div>
                </div>
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-primary btn-sm delete_selected_data"
                                    data-table-id="admin_customer_table"
                                    data-delete-url="<?php echo e(route('customers.delete')); ?>"><?php echo e(labels('admin_labels.delete_selected', 'Delete Selected')); ?></button>
                        </div>
                        <div class="col-md-12">
                            <div class="pt-0">
                                <div class="table-responsive">
                                    <table class='table' id="admin_customer_table" data-toggle="table"
                                        data-loading-template="loadingTemplate" data-url="<?php echo e(route('customers.list')); ?>"
                                        data-side-pagination="server" data-pagination="true"
                                        data-page-list="[5, 10, 20, 50, 100, 200]" data-search="false"
                                        data-show-columns="false" data-show-refresh="false" data-trim-on-search="false"
                                        data-sort-name="id" data-sort-order="desc" data-mobile-responsive="true"
                                        data-toolbar="" data-show-export="false" data-maintain-selected="true"
                                        data-export-types='["txt","excel"]' data-query-params="queryParams">
                                        <thead>
                                            <tr>
                                             
                                                <th data-field="id" data-sortable="true">
                                                    <?php echo e(labels('admin_labels.id', 'ID')); ?>

                                                <th data-field="name" data-disabled="1" data-sortable="false">
                                                    <?php echo e(labels('admin_labels.name', 'Name')); ?>

                                                </th>
                                                <th data-field="mobile" data-disabled="1" data-sortable="false">
                                                    <?php echo e(labels('admin_labels.mobile', 'Mobile')); ?>

                                                </th>
                                                
                                                <th data-field="email" data-sortable="false">
                                                    <?php echo e(labels('admin_labels.email', 'Email')); ?>

                                                </th>
                                                <th data-field="last_active" data-sortable="false">
                                                    <?php echo e(labels('admin_labels.last_active', 'Last Active')); ?>

                                                </th>
                                                <th data-field="baned" data-sortable="false">
                                                    <?php echo e(labels('admin_labels.ban', 'Ban')); ?>

                                                </th>
                                                <th data-field="status" data-sortable="false">
                                                    <?php echo e(labels('admin_labels.status', 'Status')); ?>

                                                </th>
                                                <th data-field="operate" data-sortable="false">
                                                    <?php echo e(labels('admin_labels.action', 'Action')); ?>

                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </section>
    </div>
   
<?php $__env->stopSection(); ?>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.toggle-ban').forEach(function (toggle) {
        toggle.addEventListener('change', function () {
            const userId = this.dataset.id;
            const isBanned = this.checked;

            this.disabled = true;

            fetch(`/admin/customers/${userId}/toggle-ban`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ banned: isBanned })
            })
            .then(res => res.json())
            .then(data => {
                alert(data.message);
            })
            .catch(err => {
                alert('An error occurred');
                this.checked = !isBanned;
            })
            .finally(() => {
                this.disabled = false;
            });
        });
    });
});

</script>

<?php echo $__env->make('admin/layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/pages/tables/customers.blade.php ENDPATH**/ ?>