<?php

namespace App\Http\Controllers\Admin;

use App\Enums\AdvertismentPackageType;
use App\Enums\AdvertismentStatus;
use App\Enums\AdvertismentType;
use App\Models\awfarly\Advertisment;
use App\Models\awfarly\Category;
use App\Models\awfarly\Offer;
use App\Models\awfarly\OfferClaim;
use App\Models\awfarly\ReportIssuse;
use App\Models\awfarly\Role;
use App\Models\awfarly\Store;
use App\Models\awfarly\User;
use App\Models\ComboProduct;
use App\Models\OrderItems;
use App\Models\Product;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Intervention\Image\ImageManagerStatic as Image;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class HomeController extends Controller
{

    public function index()
    {
        $id = 0;

        $store_id = getStoreId();

        $currency = fetchDetails('currencies', ['is_default' => 1], 'symbol')[0]->symbol ?? "";

        // total statictis
        $total_earnings = AdmintotalEarnings();

        $dark_mode = Auth::user() && Auth::user()->dark_mode < 1 ? 'light' : 'dark';
        $totalUsers = User::where('role_id', Role::where('name', 'customer')->value('id'))->count();
        $bannedUsers = User::where('is_banned', true)->count();
        $activeStores = Store::where('is_active', true)->count();
        $activeOffers = Offer::where('is_active', true)->count();
        $claimedOffers = OfferClaim::count();
        $adsRunning = Advertisment::whereDate('end_date', '>=', now())->count();
        $newUsersToday = User::whereDate('created_at', today())->count();
        $activeUsersToday = User::whereDate('last_active', today())->where('is_active', true)->count();
        $pandingStore = Store::where('is_approved', false)->count();
        $pendingOffers = Offer::where('is_approved', false)->count();
        $pendingAs = Advertisment::where('status', AdvertismentStatus::PENDING)->count();
        $redeemedOffers = OfferClaim::where('is_redeemed', true)->count();

        // user counter


        $user_counter = countNewUsers();
        //-----------------------------------------User growth chart------------------------------------
        $userGrowth = [
            'labels' => [], // e.g. ['Jun 1', 'Jun 2', ..., 'Jun 7']
            'data' => [],   // e.g. [2, 5, 8, 3, ...]
        ];

        $startDate = now()->subDays(30);
        for ($i = 0; $i <= 30; $i++) {
            $date = $startDate->copy()->addDays($i);
            $label = $date->format('M d');
            $count = \App\Models\User::whereDate('created_at', $date)->count();
            $userGrowth['labels'][] = $label;
            $userGrowth['data'][] = $count;
        }

        //--------------------------------- User Active Chart-----------------------------------------------
        $recentActivities = collect();

        // Fetch recent users
        $recentUsers = User::latest()->take(5)->get()->map(function ($user) {
            return [
                'type' => 'user',
                'message' => __('admin_labels.new_user_registered') . $user->username,
                'time' => $user->created_at,
                'url' => route('admin.customers.show', ['id' => $user->id]),
            ];
        });

        // Fetch recent offers
        $recentOffers = Offer::latest()->take(5)->get()->map(function ($offer) {
            return [
                'type' => 'offer',
                'message' => __('admin_labels.new_offer_created') . $offer->title_ar,
                'time' => $offer->created_at,
                'url' => route('admin.offers.show', ['id' => $offer->id]),
            ];
        });

        // Fetch recent offer claims
        $recentClaims = OfferClaim::latest()->take(5)->get()->map(function ($claim) {
            return [
                'type' => 'claim',
                'message' => __('admin_labels.offer_claimed') . $claim->user_id,
                'time' => $claim->created_at,
                'url' => route('admin.offers.show', ['id' => $claim->offer_id]),
            ];
        });

        // Merge all activities
        $recentActivities = $recentUsers
            ->merge($recentOffers)
            ->merge($recentClaims)
            ->sortByDesc('time')
            ->take(10);
        //------------------------------------ Top Stores, Offers, and Categories------------------------------------
        $topStores = Store::orderByDesc('store_views')
            ->take(5)
            ->get();

        $topOffers = Offer::with('store')
            ->orderByDesc('offer_views')
            ->take(5)
            ->get();
        // Top Categories by Claimed Offers using many-to-many relation
        $topCategories = Category::select('categories.*', DB::raw('COUNT(offer_claims.id) as claims_count'))
            ->join('offer_categories', 'categories.id', '=', 'offer_categories.category_id')
            ->join('offers', 'offer_categories.offer_id', '=', 'offers.id')
            ->join('offer_claims', 'offer_claims.offer_id', '=', 'offers.id')
            ->groupBy('categories.id')
            ->orderByDesc('claims_count')
            ->take(5)
            ->get();

        //------------------------------------ Top Active Users------------------------------------
        $topUsers = User::withCount(['claims as claims_count'])
            ->where('role_id', Role::where('name', 'customer')->value('id'))
            ->orderByDesc('claims_count')
            ->take(5)
            ->get();
            //------------------------------------ Ads Statistics------------------------------------

            $activeAds = Advertisment::where('status', AdvertismentStatus::APPROVED)->where('is_paid', 1)->where('end_date', '>=', now())->count();
$pendingAds = Advertisment::where('status', AdvertismentStatus::PENDING)->count(); 
$withForPay=Advertisment::where('status', AdvertismentStatus::APPROVED)->where('is_paid', 0)->count();
$expiredAds = Advertisment::where('status', AdvertismentStatus::APPROVED)->where('is_paid', 1)->where('end_date', '<', now())->count();
//--------------------------------------------- Ads Pie Chart------------------------------------

$bannerCount = Advertisment::where('package_type',AdvertismentPackageType::BANNER)->count();
$premuimCount = Advertisment::where('package_type', AdvertismentPackageType::PERMIUM)->count();
$popupCount = Advertisment::where('package_type',   AdvertismentPackageType::SPOTLIGHT)->count();

//--------------------------------------------- Ads Bar Chart------------------------------------
$adsPerMonth =Advertisment::select(DB::raw("COUNT(id) as count"), DB::raw("DATE_FORMAT(created_at, '%Y-%m') as month"))
->where('created_at', '>=', now()->subMonths(12))
->where('status', AdvertismentStatus::APPROVED)
->where('is_paid', 1)
->groupBy('month')
->orderBy('month')
->get();

$months = [];
$counts = [];

foreach ($adsPerMonth as $data) {
$months[] = Carbon::createFromFormat('Y-m', $data->month)->format('M Y');
$counts[] = $data->count;
}


       
        return view('admin.pages.forms.home', compact(
            'id',
            'store_id',
            'user_counter',
            'dark_mode',
            'totalUsers',
            'bannedUsers',
            'activeStores',
            'activeOffers',
            'claimedOffers',
            'adsRunning',
            'newUsersToday',
            'activeUsersToday',
            'pandingStore',
            'pendingOffers',
            'pendingAs',
            'redeemedOffers',
            'recentActivities',
            'userGrowth',
            'topStores',
            'topOffers',
            'topCategories',
            'topUsers',
            'activeAds',
            'pendingAds',
            'withForPay',
            'expiredAds',
            'bannerCount',
            'premuimCount',
            'popupCount',
            'months',
            'counts'
        ));
    }

   
    public function getLatestReports()
    {
        $latestReports = ReportIssuse::with('reporter', 'offer')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($report) {
                return [
                    'id' => $report->id,
                    'related_id' => $report->offer_id,
                    'offer_name' => $report->offer->{"title_" . app()->getLocale()} ?? 'N/A',
                    'description' => Str::limit($report->description, 80),
                    'user_name' => optional($report->reporter)->username ?? 'N/A',
                    'created_at' => $report->created_at->diffForHumans(),
                ];
            });
    
        return response()->json(['reports' => $latestReports]);
    }

   
}
