<?php

namespace App\Http\Controllers\Admin;

use App\Enums\NotificationSendTo;
use App\Enums\NotificationType;
use App\Jobs\PushNotificationJob;
use App\Models\awfarly\Notification;
use App\Models\awfarly\Role;
use App\Models\awfarly\User;
use App\Models\Category;
use App\Models\UserFcmToken;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/**
 * NotificationController handles all notification-related operations for the admin panel
 *
 * This controller manages:
 * - Push notifications to mobile devices via FCM
 * - Email notifications to sellers
 * - Notification listing and management
 * - User-specific and broadcast notifications
 */
class NotificationController extends Controller
{
  
    /**
     * Display the push notification form page
     *
     * @return \Illuminate\View\View Returns the push notification form view
     */
    public function index()
    {
        return view('admin.pages.forms.send_notification');
    }

    /**
     * Display the seller email notification form page
     *
     * @return \Illuminate\View\View Returns the seller email notification form view
     */
    public function seller_email_notification_index()
    {
        return view('admin.pages.forms.seller_email_notification');
    }

    /**
     * Store and send push notifications to users
     *
     * This method handles the creation and sending of push notifications via FCM.
     * It supports both specific user targeting and broadcast to all customers.
     *
     * @param Request $request The HTTP request containing notification data
     * @return \Illuminate\Http\JsonResponse JSON response indicating success or failure
     */
    public function store(Request $request)
    {
        try {
            // Define validation rules for the notification form
            $rules = [
                'send_to' => 'required',
                'title' => 'required',
                'message' => 'required',
            ];

            // Add additional validation for specific user selection
            if ($request->send_to === 'specific_user') {
                $rules['select_user_id'] = 'required|array';
            }

            // Validate the incoming request data
            $validator = Validator::make($request->all(), $rules);

            // Handle validation failures
            if ($validator->fails()) {
                if ($request->ajax()) {
                    return response()->json(['errors' => $validator->errors()->all()], 422);
                }
                return redirect()->back()->withErrors($validator)->withInput();
            }

            // Extract notification data from request
            $title = $request->input('title');
            $message = $request->input('message');
            $sendTo = $request->input('send_to');
            $fcm_ids = [];

            // Handle specific user targeting
            if ($sendTo === 'specific_user') {
                // Get selected user IDs and their FCM tokens
                $userIds = $request->input("select_user_id", []);
                $results = UserFcmToken::whereIn('user_id', $userIds)->pluck('fcm_token');

                // Filter out empty tokens and convert to array
                $fcm_ids = $results->filter()->toArray();

                // Check if any valid FCM tokens were found
                if (empty($fcm_ids)) {
                    return response()->json([
                        'error' => true,
                        'error_message' => __('admin_labels.fcm_ids_not_set')
                    ]);
                }
            } else {
                // Send to all customers - get customer role ID
                $roleId = Role::where('name', 'customer')->first()->id;

                // Get FCM tokens for all customers
                $fcm_ids = UserFcmToken::whereHas('user', function ($q) use ($roleId) {
                    $q->where('role_id', $roleId);
                })->pluck('fcm_token')->filter()->toArray();

                // Check if any customer FCM tokens were found
                if (empty($fcm_ids)) {
                    return response()->json([
                        'error' => true,
                        'error_message' => __('admin_labels.fcm_ids_not_set')
                    ]);
                }
            }

            // Create and save the notification record in database
            $notification = new Notification();
            $notification->send_to = $sendTo === 'specific_user' ? NotificationSendTo::SPECIFIC : NotificationSendTo::ALL;
            $notification->title = $title;
            $notification->message = $message;
            $notification->type = NotificationType::GENERAL;
            $notification->save();

            // Attach specific users to the notification if targeting specific users
            if ($sendTo === 'specific_user') {
                $notification->users()->attach($request->select_user_id);
            }

            // Split FCM tokens into chunks of 500 for efficient processing
            // FCM has limits on batch size, so we process in chunks
            $chunks = array_chunk($fcm_ids, 500);
            foreach ($chunks as $chunk) {
                // Dispatch push notification job for each chunk
                PushNotificationJob::dispatch($chunk, $title, $message, []);
            }

            // Return success response
            return response()->json([
                'error' => false,
                'message' => labels('admin_labels.notification_sent_successfully', 'Notification sent successfully')
            ]);
        }
        catch(\Exception $e) {
            // Log the error for debugging purposes
            Log::error('Error sending notification: ' . $e->getMessage());

            // Return error response
            return response()->json([
                'error' => true,
                'error_message' => __('admin_labels.something_went_wrong')
            ]);
        }
    }

    /**
     * Store and send email notifications to sellers
     *
     * This method handles sending email notifications to sellers via SMTP.
     * It supports both targeting all sellers or specific selected sellers.
     *
     * @param Request $request The HTTP request containing email notification data
     * @return \Illuminate\Http\JsonResponse JSON response indicating success or failure
     */
    public function store_email_notification(Request $request)
    {
        // Define base validation rules for email notification
        $rules = [
            'send_to' => 'required|in:all_sellers,specific_seller',
            'subject' => 'required|string',
            'message' => 'required|string',
        ];

        // Add conditional validation for specific seller selection
        if ($request->send_to === 'specific_seller') {
            $rules['select_user_id'] = 'required|array|min:1';
        }

        // Validate the incoming request data
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $errors = $validator->errors();
            return $request->ajax()
                ? response()->json(['errors' => $errors->all()], 422)
                : redirect()->back()->withErrors($errors)->withInput();
        }

        // Fetch and validate SMTP email settings from system configuration
        $emailSettings = json_decode(getSettings('email_settings', true), true);
        if (empty($emailSettings['email']) || empty($emailSettings['password']) || empty($emailSettings['smtp_host'])) {
            return response()->json([
                'error' => true,
                'error_message' => labels('admin_labels.please_add_smtp_settings_from_email_setting', 'Please add SMTP settings from email setting'),
            ]);
        }

        // Extract email data from request
        $subject = $request->input('subject');
        $messageContent = $request->input('message');
        $sendTo = $request->input('send_to');

        // Determine target users based on send_to option
        $users = collect();
        if ($sendTo === 'all_sellers') {
            // Get all active sellers
            $sellerRoleId = Role::where('name', 'seller')->value('id');
            $users = User::where('status', 1)->where('role_id', $sellerRoleId)->get();
        } else {
            // Get specific selected sellers
            $userIds = $request->input('select_user_id', []);
            $users = User::whereIn('id', $userIds)->get();
        }

        // Send email to each target user
        foreach ($users as $user) {
            Mail::send([], [], function ($message) use ($user, $subject, $messageContent) {
                $message->to($user->email)
                    ->subject($subject)
                    ->html($messageContent);
            });
        }

        // Return success response
        return response()->json([
            'error' => false,
            'message' => labels('admin_labels.mail_sent_successfully', 'Mail sent successfully'),
        ]);
    }
    


    /**
     * Get paginated list of notifications for admin panel
     *
     * This method retrieves notifications with pagination, sorting, and search functionality
     * for display in the admin panel's notification management interface.
     *
     * @param Request $request The HTTP request containing pagination and search parameters
     * @return \Illuminate\Http\JsonResponse JSON response with notification data and pagination info
     */
    public function list(Request $request)
    {
        // Extract pagination and search parameters from request
        $offset = $request->input('pagination_offset', 0);
        $limit = $request->input('limit', 10);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'ASC');
        $search = trim($request->input('search', ''));

        // Initialize the notification query
        $query = Notification::query();

        // Optional: add filtering if store-based notifications in future
        // $query->where('store_id', $store_id);

        // Apply search filter if search term is provided
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('id', $search)
                  ->orWhere('title', 'LIKE', "%{$search}%")
                  ->orWhere('message', 'LIKE', "%{$search}%");
            });
        }

        // Get total count for pagination (clone query to avoid affecting main query)
        $total = (clone $query)->count();

        // Fetch paginated and sorted notifications
        $notifications = $query
            ->orderBy($sort, $order)
            ->skip($offset)
            ->take($limit)
            ->get();

        // Format notification data for frontend display
        $rows = [];
        foreach ($notifications as $notification) {
            $rows[] = [
                'id' => $notification->id,
                'title' => $notification->title,
                'type' => $notification->type,
                'message' => $notification->message,
                'send_to' => $notification->send_to->label(), // Get human-readable label for send_to enum
            ];
        }

        // Return paginated response
        return response()->json([
            'total' => $total,
            'rows' => $rows,
        ]);
    }
    
 
    /**
     * Get comma-separated usernames by user IDs
     *
     * This utility method takes a JSON-encoded string of user IDs and returns
     * a comma-separated string of corresponding usernames.
     *
     * @param string $user_ids JSON-encoded string containing array of user IDs
     * @return string Comma-separated string of usernames, empty string if no users found
     */
    public function get_users_by_ids($user_ids)
    {
        // Decode the JSON-encoded string into an array
        $ids_array = json_decode($user_ids);

        // Return empty string if no valid IDs provided
        if (empty($ids_array)) {
            return '';
        }

        // Fetch users based on the provided IDs
        $users = User::whereIn('id', $ids_array)->get();

        // Extract usernames from the User models
        $users_array = $users->pluck('username')->toArray();

        // Join usernames into a comma-separated string
        $comma_separated_users = implode(',', $users_array);

        return $comma_separated_users;
    }

    /**
     * Get notifications for API consumption (mobile app/frontend)
     *
     * This method retrieves notifications with pagination and filtering for API endpoints.
     * It supports user-specific filtering and includes category data for category-type notifications.
     *
     * @param int $offset Starting position for pagination
     * @param int $limit Number of records to retrieve
     * @param string $sort Field to sort by
     * @param string $order Sort order (ASC/DESC)
     * @param string $user_id Optional user ID to filter notifications for specific user
     * @return array Array containing total count and notification data
     */
    public function get_notifications($offset, $limit, $sort, $order, $user_id = '')
    {
        $notificationData = [];

        // Get total count of notifications for pagination
        $countRes = DB::table('notifications')->select(DB::raw('COUNT(id) as total'))->get()->toArray();

        // Build query to filter notifications based on user_id or get all general notifications
        $query = Notification::orderBy($sort, $order)
            ->where(function ($q) use ($user_id) {
                // Include notifications sent to all users or specifically to this user
                $q->where('send_to', 'all_users')
                    ->orWhereJsonContains('users_id', (string) $user_id);
            })
            ->limit($limit)
            ->offset($offset);

        // Execute query and convert to array
        $searchRes = $query->get()->toArray();

        // Process each notification to format data for API response
        foreach ($searchRes as $key => $notification) {
            // Ensure required fields have default values
            $searchRes[$key]['title'] = $notification['title'] ?? '';
            $searchRes[$key]['message'] = $notification['message'] ?? '';
            $searchRes[$key]['send_to'] = $notification['send_to'] ?? '';

            // Convert users_id JSON to comma-separated string
            $usersId = !empty($notification['users_id']) ? implode(',', json_decode($notification['users_id'], true)) : '';
            $searchRes[$key]['users_id'] = $usersId;
            $searchRes[$key]['link'] = $notification['link'] ?? '';

            // Convert image path to full URL if image exists
            if (!empty($notification['image'])) {
                $searchRes[$key]['image'] = getMediaImageUrl($notification['image']);
            }

            // Fetch and format category data for category-type notifications
            if ($searchRes[$key]['type'] === 'categories' && !empty($searchRes[$key]['type_id'])) {
                $categoryData = Category::where('id', $searchRes[$key]['type_id'])->first();
                $subcategories = Category::where('parent_id', $searchRes[$key]['type_id'])->get()->toArray();

                if ($categoryData) {
                    // Convert category data to array and format image URLs
                    $categoryData = $categoryData->toArray();
                    $categoryData['image'] = getMediaImageUrl($categoryData['image']);
                    $categoryData['banner'] = getMediaImageUrl($categoryData['banner']);
                    $categoryData['children_count'] = count($subcategories);

                    // Format subcategory image URLs
                    foreach ($subcategories as &$subcategory) {
                        $subcategory['image'] = getMediaImageUrl($subcategory['image'] ?? "");
                        $subcategory['banner'] = getMediaImageUrl($subcategory['banner'] ?? "");
                    }

                    // Add subcategories to category data
                    $categoryData['children'] = $subcategories;
                    $searchRes[$key]['category_data'] = $categoryData;
                }
            }
        }

        // Prepare final response data
        $notificationData['total'] = $countRes[0]->total;
        $notificationData['data'] = $searchRes;

        return $notificationData;
    }
 
  
}
