<?php

namespace App\Http\Controllers\Admin;

use App\Enums\NotificationSendTo;
use App\Enums\NotificationType;
use App\Jobs\PushNotificationJob;
use App\Models\awfarly\Notification;
use App\Models\awfarly\Role;
use App\Models\awfarly\User;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Setting;
use App\Models\UserFcm;
use App\Models\UserFcmToken;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class NotificationController extends Controller
{
    protected $categoryController;
    public function __construct(CategoryController $categoryController)
    {
        $this->categoryController = $categoryController;
    }
    public function index()
    {
        return view('admin.pages.forms.send_notification');
    }
   
    public function seller_email_notification_index()
    {
        return view('admin.pages.forms.seller_email_notification');
    }

    public function store(Request $request)
    {
        try{
        $rules = [
            'send_to' => 'required',
            'title' => 'required',
            'message' => 'required',
        ];

        if ($request->send_to === 'specific_user') {
            $rules['select_user_id'] = 'required|array';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json(['errors' => $validator->errors()->all()], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $title = $request->input('title');
        $message = $request->input('message');
        $sendTo = $request->input('send_to');
        $fcm_ids = [];

        if ($sendTo === 'specific_user') {
            $userIds = $request->input("select_user_id", []);
            $results = UserFcmToken::whereIn('user_id', $userIds)->pluck('fcm_token');

            $fcm_ids = $results->filter()->toArray();

            if (empty($fcm_ids)) {
                return response()->json([
                    'error' => true,
                    'error_message' => __('admin_labels.fcm_ids_not_set')
                ]);
            }
        } else {
            // Send to all customers
            $roleId = Role::where('name', 'customer')->first()->id;

            $fcm_ids = UserFcmToken::whereHas('user', function ($q) use ($roleId) {
                $q->where('role_id', $roleId);
            })->pluck('fcm_token')->filter()->toArray();

            if (empty($fcm_ids)) {
                return response()->json([
                    'error' => true,
                    'error_message' => __('admin_labels.fcm_ids_not_set')
                ]);
            }
        }

        // $fcmMsg = [
        //     'title' => $title,
        //     'body' => $message,
        // ];

        $notification = new Notification();
        $notification->send_to = $sendTo === 'specific_user' ? NotificationSendTo::SPECIFIC : NotificationSendTo::ALL;
        $notification->title = $title;
        $notification->message = $message;
        $notification->type = NotificationType::GENERAL;
        $notification->save();

        if ($sendTo === 'specific_user') {
            $notification->users()->attach($request->select_user_id);
        }

        $chunks = array_chunk($fcm_ids, 500);
        foreach ($chunks as $chunk) {
            PushNotificationJob::dispatch($chunk, $title, $message, []);
            // sendNotification('', $chunk, $fcmMsg);
        }

        return response()->json([
            'error' => false,
            'message' => labels('admin_labels.notification_sent_successfully', 'Notification sent successfully')
        ]);
    }
    catch(\Exception $e)
    {
        Log::error('Error sending notification: ' . $e->getMessage());
        return response()->json([
            'error' => true,
            'error_message' => __('admin_labels.something_went_wrong')
        ]);
    }
    }

    public function store_email_notification(Request $request)
    {
        // Base validation rules
        $rules = [
            'send_to' => 'required|in:all_sellers,specific_seller',
            'subject' => 'required|string',
            'message' => 'required|string',
        ];
    
        // Add conditional validation if specific seller is selected
        if ($request->send_to === 'specific_seller') {
            $rules['select_user_id'] = 'required|array|min:1';
        }
    
        // Run validation
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $errors = $validator->errors();
            return $request->ajax()
                ? response()->json(['errors' => $errors->all()], 422)
                : redirect()->back()->withErrors($errors)->withInput();
        }
    
        // Fetch SMTP email settings
        $emailSettings = json_decode(getSettings('email_settings', true), true);
        if (empty($emailSettings['email']) || empty($emailSettings['password']) || empty($emailSettings['smtp_host'])) {
            return response()->json([
                'error' => true,
                'error_message' => labels('admin_labels.please_add_smtp_settings_from_email_setting', 'Please add SMTP settings from email setting'),
            ]);
        }
    
        // Prepare email data
        $subject = $request->input('subject');
        $messageContent = $request->input('message');
        $sendTo = $request->input('send_to');
    
        // Fetch target users
        $users = collect();
        if ($sendTo === 'all_sellers') {
            $sellerRoleId = Role::where('name', 'seller')->value('id');
            $users = User::where('status', 1)->where('role_id', $sellerRoleId)->get();
        } else {
            $userIds = $request->input('select_user_id', []);
            $users = User::whereIn('id', $userIds)->get();
        }
    
        // Send emails
        foreach ($users as $user) {
            Mail::send([], [], function ($message) use ($user, $subject, $messageContent) {
                $message->to($user->email)
                    ->subject($subject)
                    ->html($messageContent);
            });
        }
    
        return response()->json([
            'error' => false,
            'message' => labels('admin_labels.mail_sent_successfully', 'Mail sent successfully'),
        ]);
    }
    


    public function list(Request $request)
    {
        $offset = $request->input('pagination_offset', 0);
        $limit = $request->input('limit', 10);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'ASC');
        $search = trim($request->input('search', ''));
    
        $query = Notification::query();
    
        // Optional: add filtering if store-based notifications in future
        // $query->where('store_id', $store_id);
    
        // Apply search filter
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('id', $search)
                  ->orWhere('title', 'LIKE', "%{$search}%")
                  ->orWhere('message', 'LIKE', "%{$search}%");
            });
        }
    
        // Clone for counting
        $total = (clone $query)->count();
    
        // Fetch data
        $notifications = $query
            ->orderBy($sort, $order)
            ->skip($offset)
            ->take($limit)
            ->get();
    
        $rows = [];
    
        foreach ($notifications as $notification) {
    
            $rows[] = [
                'id' => $notification->id,
                'title' => $notification->title,
                'type' => $notification->type,
                'message' => $notification->message,
                'send_to' => $notification->send_to->label(),
            ];
        }
    
        return response()->json([
            'total' => $total,
            'rows' => $rows,
        ]);
    }
    
 
    public function get_users_by_ids($user_ids)
    {
        // Decode the JSON-encoded string into an array
        $ids_array = json_decode($user_ids);

        // Ensure the array is not empty
        if (empty($ids_array)) {
            return '';
        }

        // Fetch the users based on the array of IDs
        $users = User::whereIn('id', $ids_array)->get();

        // Extract the 'username' attribute from each User model
        $users_array = $users->pluck('username')->toArray();

        // Join the usernames into a comma-separated string
        $comma_separated_users = implode(',', $users_array);

        return $comma_separated_users;
    }

    public function get_notifications($offset, $limit, $sort, $order, $user_id = '')
    {
        $notificationData = [];

        // Calculate total number of notifications
        $countRes = DB::table('notifications')->select(DB::raw('COUNT(id) as total'))->get()->toArray();

        // Adjust the query to filter by user_id if provided, or to include all general notifications
        $query = Notification::orderBy($sort, $order)
            ->where(function ($q) use ($user_id) {
                $q->where('send_to', 'all_users')
                    ->orWhereJsonContains('users_id', (string) $user_id);
            })

            ->limit($limit)
            ->offset($offset);

        $searchRes = $query->get()->toArray();

        foreach ($searchRes as $key => $notification) {
            $searchRes[$key]['title'] = $notification['title'] ?? '';
            $searchRes[$key]['message'] = $notification['message'] ?? '';
            $searchRes[$key]['send_to'] = $notification['send_to'] ?? '';
            $usersId = !empty($notification['users_id']) ? implode(',', json_decode($notification['users_id'], true)) : '';
            $searchRes[$key]['users_id'] = $usersId;
            $searchRes[$key]['link'] = $notification['link'] ?? '';

            // Handling the image path conversion
            if (!empty($notification['image'])) {
                $searchRes[$key]['image'] = getMediaImageUrl($notification['image']);
            }

            // Fetch category data if type is 'categories'
            if ($searchRes[$key]['type'] === 'categories' && !empty($searchRes[$key]['type_id'])) {
                $categoryData = Category::where('id', $searchRes[$key]['type_id'])->first();
                $subcategories = Category::where('parent_id', $searchRes[$key]['type_id'])->get()->toArray();

                if ($categoryData) {
                    $categoryData = $categoryData->toArray();
                    $categoryData['image'] = getMediaImageUrl($categoryData['image']);
                    $categoryData['banner'] = getMediaImageUrl($categoryData['banner']);
                    $categoryData['children_count'] = count($subcategories);

                    foreach ($subcategories as &$subcategory) {
                        $subcategory['image'] = getMediaImageUrl($subcategory['image'] ?? "");
                        $subcategory['banner'] = getMediaImageUrl($subcategory['banner'] ?? "");
                    }

                    $categoryData['children'] = $subcategories;
                    $searchRes[$key]['category_data'] = $categoryData;
                }
            }
        }

        $notificationData['total'] = $countRes[0]->total;
        $notificationData['data'] = $searchRes;

        return $notificationData;
    }
 
  
}
