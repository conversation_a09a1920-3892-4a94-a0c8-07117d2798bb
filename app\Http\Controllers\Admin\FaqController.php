<?php

namespace App\Http\Controllers\Admin;

use App\Models\Faq;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\HtmlString;

/**
 * FaqController handles all FAQ-related operations in the admin panel
 *
 * This controller manages CRUD operations for FAQs including:
 * - Creating new FAQs
 * - Listing and searching FAQs
 * - Updating FAQ content and status
 * - Deleting single or multiple FAQs
 */
class FaqController extends Controller
{
    /**
     * Display the FAQ management page
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('admin.pages.forms.faqs');
    }

    /**
     * Store a new FAQ in the database
     *
     * Validates the incoming request data and creates a new FAQ entry.
     * Supports both AJAX and regular form submissions.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate the incoming request data
        $validator = Validator::make($request->all(), [
            'question' => 'required',
            'answer' => 'required',
        ]);

        // Handle validation failures
        if ($validator->fails()) {
            $errors = $validator->errors();

            // Return JSON response for AJAX requests
            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            // Redirect back with errors for regular form submissions
            return redirect()->back()->withErrors($errors)->withInput();
        }

        // Prepare validated data for storage
        $validatedData = $validator->validated();
        $validatedData['question'] = $request->question;
        $validatedData['answer'] = $request->answer;
        $validatedData['status'] = 1; // Set status to active by default

        // Create the new FAQ
        Faq::create($validatedData);

        // Return success response for AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'message' => labels('admin_labels.faq_added_successfully', 'Faq added successfully')
            ]);
        }
    }

    /**
     * Get paginated list of FAQs with search functionality
     *
     * This method handles the data table display for FAQs in the admin panel.
     * It supports searching, sorting, and pagination.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function list()
    {
        // Get request parameters with defaults
        $search = trim(request('search'));
        $sort = (request('sort')) ? request('sort') : "id";
        $order = (request('order')) ? request('order') : "DESC";
        $offset = $search || (request('pagination_offset')) ? (request('pagination_offset')) : 0;
        $limit = (request('limit')) ? request('limit') : "10";

        // Build query with optional search functionality
        $faqs = Faq::when($search, function ($query) use ($search) {
            return $query->where('question', 'like', '%' . $search . '%')
                ->orWhere('answer', 'like', '%' . $search . '%');
        });

        // Get total count before applying pagination
        $total = $faqs->count();

        // Apply sorting and pagination, then transform data for display
        $faqs = $faqs->orderBy($sort, $order)
            ->offset($offset)
            ->limit($limit)
            ->get()
            ->map(function ($f) {
                // Generate action URLs for each FAQ
                $edit_url = route('faqs.edit', $f->id);
                $delete_url = route('faqs.destroy', $f->id);

                // Create dropdown action menu HTML
                $action = '<div class="dropdown bootstrap-table-dropdown" dir="ltr">
                <a href="#" class="text-dark" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="bx bx-dots-horizontal-rounded"></i>
                </a>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                <a class="dropdown-item dropdown_menu_items edit-faq" data-id="' . $f->id . '"><i class="bx bx-pencil mx-2"></i> '. __('admin_labels.edit') .'</a>
                <a class="dropdown-item delete-data dropdown_menu_items" data-url="' . $delete_url . '"><i class="bx bx-trash mx-2"></i>'. __('admin_labels.delete').' </a>
                </div>
            </div>';

                // Return formatted data for data table
                return [
                    'id' => $f->id,
                    'question' => $f->question,
                    'answer' => $f->answer,
                    'operate' => $action,
                ];
            });

        // Return JSON response for data table
        return response()->json([
            "rows" => $faqs,
            "total" => $total,
        ]);
    }

    /**
     * Toggle the status of a FAQ (active/inactive)
     *
     * @param int $id The FAQ ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function update_status($id)
    {
        // Find the FAQ or throw 404 if not found
        $faq = Faq::findOrFail($id);

        // Toggle status between active (1) and inactive (0)
        $faq->status = $faq->status == '1' ? '0' : '1';
        $faq->save();

        return response()->json(['success' => labels('admin_labels.status_updated_successfully', 'Status updated successfully.')]);
    }

    /**
     * Delete a specific FAQ
     *
     * @param int $id The FAQ ID to delete
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $faq = Faq::find($id);

        if ($faq) {
            $faq->delete();
            return response()->json([
                'error' => false,
                'message' => labels('admin_labels.faq_deleted_successfully', 'Faq deleted successfully!')
            ]);
        } else {
            return response()->json(['error' => labels('admin_labels.data_not_found', 'Data Not Found')]);
        }
    }

    /**
     * Get a specific FAQ for editing
     *
     * @param int $id The FAQ ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit($id)
    {
        $faq = Faq::find($id);

        if (!$faq) {
            return response()->json([
                'error' => true,
                'message' => labels('admin_labels.data_not_found', 'Data Not Found')
            ], 404);
        }

        return response()->json($faq);
    }

    /**
     * Update an existing FAQ
     *
     * Validates the incoming request data and updates the specified FAQ.
     * Supports both AJAX and regular form submissions.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id The FAQ ID to update
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        // Find the FAQ to update
        $faq = Faq::find($id);
        if (!$faq) {
            return response()->json([
                'error' => true,
                'message' => labels('admin_labels.data_not_found', 'Data Not Found')
            ], 404);
        } else {
            // Validate the incoming request data
            $validator = Validator::make($request->all(), [
                'edit_question' => 'required',
                'edit_answer' => 'required',
            ]);

            // Handle validation failures
            if ($validator->fails()) {
                $errors = $validator->errors();

                // Return JSON response for AJAX requests
                if ($request->ajax()) {
                    return response()->json(['errors' => $errors->all()], 422);
                }
                // Redirect back with errors for regular form submissions
                return redirect()->back()->withErrors($errors)->withInput();
            }

            // Update FAQ data
            $faq->question = $request->input('edit_question');
            $faq->answer = $request->input('edit_answer');
            $faq->save();

            // Return success response for AJAX requests
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.faq_updated_successfully', 'Faq updated successfully')
                ]);
            }
        }
    }

    /**
     * Get active FAQs for public display
     *
     * This method retrieves active FAQs with pagination and data sanitization
     * for public consumption (e.g., frontend FAQ display).
     *
     * @param int $offset Starting position for pagination
     * @param int $limit Number of items to retrieve
     * @param string $sort Field to sort by
     * @param string $order Sort direction (ASC/DESC)
     * @return array Array containing total count and FAQ data
     */
    public function getFaqs($offset, $limit, $sort, $order)
    {
        $faqs_data = [];

        // Get total count of active FAQs
        $totalCount = Faq::where('status', '1')->count();

        // Retrieve active FAQs with pagination and sorting
        $faqs = Faq::where('status', '1')
            ->orderBy($sort, $order)
            ->skip($offset)
            ->take($limit)
            ->get();

        // Process each FAQ for public display
        $faqs = $faqs->map(function ($faq) {
            // Remove timestamp fields for cleaner output
            unset($faq['created_at']);
            unset($faq['updated_at']);

            // Convert null values to empty strings for consistency
            foreach ($faq as $key => $value) {
                if ($value === null) {
                    $faq[$key] = '';
                }
            }

            // Escape HTML characters for security
            return outputEscaping($faq->toArray());
        });

        // Prepare response data
        $faqs_data['total'] = $totalCount;
        $faqs_data['data'] = $faqs;

        return $faqs_data;
    }

    /**
     * Delete multiple selected FAQs
     *
     * This method handles bulk deletion of FAQs based on provided IDs.
     * It validates that all provided IDs exist before deletion.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete_selected_data(Request $request)
    {
        // Validate that IDs are provided and exist in the database
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:faqs,id'
        ]);

        // Delete each FAQ by ID
        foreach ($request->ids as $id) {
            $faq = Faq::find($id);

            if ($faq) {
                Faq::where('id', $id)->delete();
            }
        }

        // Return success response
        return response()->json([
            'error' => false,
            'message' => labels('admin_labels.faq_deleted_successfully', 'Selected faqs deleted successfully!'),
        ]);
    }
}
