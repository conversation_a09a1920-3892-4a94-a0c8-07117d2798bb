<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\AddCityRequest;
use App\Models\awfarly\City;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AreaController extends Controller
{


    // city

    public function displayCity()
    {
        return view('admin.pages.forms.city');
    }

    public function storeCity(AddCityRequest $request)
    {
        try{
        $city = new City();
        $city->name_ar = $request->name_ar;
        $city->name_en = $request->name_en;

        $city->save();

        if ($request->ajax()) {
            return response()->json(['message' => labels('admin_labels.city_added_successfully', 'City added successfully')]);
        }
    }catch(\Exception $e){
        Log::error('Error creating city: ' . $e->getMessage(), [
            'exception' => $e,
            'request_data' => $request->all(),
        ]);

        return back()->withErrors(['error' => __('admin_labels.something_went_wrong')]);
    }
    }


    public function cityList(Request $request)
    {
        try {
            $search = trim(request('search'));
            $sort = (request('sort')) ? request('sort') : "id";
            $order = (request('order')) ? request('order') : "DESC";
            $offset = $search || (request('pagination_offset')) ? (request('pagination_offset')) : 0;
            $limit = (request('limit')) ? request('limit') : "10";

            $city_data = City::when($search, function ($query) use ($search) {
                return $query->where('name_ar', 'like', '%' . $search . '%')
                    ->orWhere('name_en', 'like', '%' . $search . '%');
            });

            $total = $city_data->count();

            // Use Paginator to handle the server-side pagination
            $cities = $city_data->orderBy($sort, $order)->offset($offset)
                ->limit($limit)
                ->get();

            // Prepare the data for the "Actions" field
            $data = $cities->map(function ($c) {
                $delete_url = route('admin.city.destroy', $c->id);
                $action = '<div class="dropdown bootstrap-table-dropdown" dir="ltr">
                <a href="#" class="text-dark" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="bx bx-dots-horizontal-rounded"></i>
                </a>
                <div class="dropdown-menu table_dropdown city_action_dropdown" aria-labelledby="dropdownMenuButton">
                <a class="dropdown-item dropdown_menu_items edit-city" data-id="' . $c->id . '"><i class="bx bx-pencil mx-2"></i> Edit</a>
                    <a class="dropdown-item delete-data dropdown_menu_items" data-url="' . $delete_url . '"><i class="bx bx-trash mx-2"></i> Delete</a>
                </div>
            </div>';

                return [
                    'id' => $c->id,
                    'name_ar' => $c->name_ar,
                    'name_en' => $c->name_en,
                    'operate' => $action,
                ];
            });

            return response()->json([
                "rows" => $data, // Return the formatted data for the "Actions" field
                "total" => $total,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching city list: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
            return response()->json([
                "rows" => [],
                "total" => 0,
            ]);
        }
    }

    public function cityDestroy($id)
    {
        $city = City::find($id);
        if (isForeignKeyInUse('zipcodes', 'city_id', $id)) {
            return response()->json(['error' => labels('admin_labels.you_cannot_delete_this_city_because_it_is_assoicated_with_zipcode', 'You cannot delete this city because it is associated with zipcode.')]);
        }

        if ($city->delete()) {
            return response()->json(['error' => false, 'message' => labels('admin_labels.city_deleted_successfully', 'City deleted successfully')]);
        }
        return response()->json(['error' => labels('admin_labels.something_went_wrong', 'Something went wrong')]);
    }

    public function getCities(Request $request)
    {
        $search = trim($request->search);

        $cities = City::where('name', 'like', '%' . $search . '%')->get();

        $data = array();
        foreach ($cities as $city) {
            $data[] = array("id" => $city->id, "text" => $city->name);
        }
        return response()->json($data);
    }

    public function getCitiesList($sort = "c.name", $order = "ASC", $search = "", $limit = '', $offset = '')
    {

        $query = City::select('cities.*')
            ->leftJoin('areas', 'cities.id', '=', 'areas.city_id');

        if (!empty($search)) {
            $query->where('cities.name', 'like', '%' . $search . '%');
        }
        $totalRecords = $query->count();
        $cities = $query->orderBy($sort, $order)
            ->skip($offset)
            ->take($limit)
            ->get();

        // Remove created_at and updated_at fields from each item in the collection
        $cities->each(function ($item) {

            unset($item->created_at);
            unset($item->updated_at);
        });

        $bulkData = [
            'error' => $cities->isEmpty(),
            'total' => $totalRecords,
            'language_message_key' => 'cities_retrived_successfully',
            'data' => $cities->isEmpty() ? [] : $cities->toArray(),
        ];


        return response()->json($bulkData);
    }


    public function cityEdit($id)
    {
        return $this->editData(City::class, $id, labels('admin_labels.data_not_found', 'Data Not Found'));
    }

    public function cityUpdate(Request $request, $id)
    {
        $fields = ['name_ar', 'name_en',];

        return $this->updateData(
            $request,
            City::class,
            $id,
            $fields,
            labels('admin_labels.city_updated_successfully', 'City updated successfully')
        );
    }

    // general function for fetch edit data

    public function editData($model_name, $id, $error_message)
    {
        $data = $model_name::find($id);

        if (!$data) {
            return response()->json(['error' => true, 'message' => $error_message], 404);
        }
        return response()->json($data);
    }

    // general function for update fetched data

    public function updateData(Request $request, $model_name, $id, $fields, $success_message)
    {
        $data = $model_name::find($id);

        if (!$data) {
            return response()->json(['error' => true, 'message' => labels('admin_labels.data_not_found', 'Data Not Found')], 404);
        } else {
            // Define validation rules
            $rules = [];
            foreach ($fields as $field) {
                $rules[$field] = 'required'; // Modify as needed
            }

            // Validate the request
            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                $errors = $validator->errors();

                if ($request->ajax()) {
                    return response()->json(['errors' => $errors->all()], 422);
                }
                return redirect()->back()->withErrors($errors)->withInput();
            }

            // Update the data
            if (count($fields) === 1) {
                $field = reset($fields);
                $data->{strtolower($field)} = $request->input($field);
            } else {
                foreach ($fields as $field) {
                    $data->{strtolower($field)} = $request->input($field);
                }
            }

            $data->save();

            if ($request->ajax()) {
                return response()->json(['message' => $success_message]);
            }
        }
    }



    public function delete_selected_city_data(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:cities,id'
        ]);

        foreach ($request->ids as $id) {
            $city = City::find($id);

            if ($city) {
                City::where('id', $id)->delete();
            }
        }

        return response()->json([
            'error' => false,
            'message' => labels('admin_labels.cities_deleted_successfully', 'Selected cities deleted successfully!'),
        ]);
    }
}
