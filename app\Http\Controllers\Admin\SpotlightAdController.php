<?php

namespace App\Http\Controllers\Admin;

use App\Enums\AdvertismentPackageType;
use App\Enums\AdvertismentStatus;
use App\Enums\AdvertismentType;
use App\Enums\UserRole;
use App\Http\Requests\AddSpotlightRequest;
use App\Models\awfarly\Advertisment;
use App\Models\awfarly\Offer;
use App\Models\awfarly\Store;
use App\Models\awfarly\User;
use App\Models\Promocode;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;

class SpotlightAdController extends Controller
{
    public function index()
    {
        $locale = app()->getLocale();
        $offers = Offer::select('id', 'title_' . $locale . ' as title')
            ->where('expire_date', '>=', Carbon::now())
            ->get();

        $stores = Store::select('id', 'name_' . $locale . ' as name')->where("is_active", 1)->get();

        return view('admin.pages.tables.manage_spotlight', compact('offers', 'stores'));
    }

    public function store(AddSpotlightRequest $request)
    {

        try {
            Log::info('request', [$request->all()]);
            // Create the spotlight ad record
            Advertisment::create([
                'title_ar'          => 'اعلان مثبت',
                'title_en'          => 'Spotlight Ad',
                'is_paid'           => 1,
                'created_by_type'   => UserRole::ADMIN, // Created by admin
                'created_by_id'     => auth()->id(),
                'offer_id'          => $request->advertisement_type == 'OFFER' ? $request->offer_id : null,
                'store_id'          => $request->advertisement_type == 'STORE' ? $request->store_id : null,
                'start_date'        => $request->start_date,
                'end_date'          => $request->end_date,
                'advertisement_type' => $request->advertisement_type,
                'package_type'      => AdvertismentPackageType::SPOTLIGHT,
                'status'            => AdvertismentStatus::APPROVED,
            ]);

            // Return success message (AJAX or redirect)
            return $request->ajax()
                ? response()->json(['message' => labels('admin_labels.spotlight_ads_created_successfully', 'spotlight Ad created successfully')])
                : redirect()->back()->with('success', labels('admin_labels.spotlight_ads_created_successfully', 'spotlight Ad created successfully'));
        } catch (Exception $e) {
            // Log error details for debugging
            Log::error('Error creating slider: ' . $e->getMessage(), [
                'exception' => $e,
                'request_data' => $request->except(['image']),
            ]);

            // Return generic error message
            return back()->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }
    /**
     * Fetch and return a paginated list of spotlight advertisements.
     * Supports search, sorting, filtering by status and creation date range.
     * 
     * @param  \App\Http\Requests\Request  $request
     * @return \Illuminate\Http\JsonResponse

     */
    public function list(Request $request)
    {
        // Get query parameters from the request, with default fallbacks
        $search     = trim($request->input('search', ''));
        $sort       = $request->input('sort', 'id');
        $order      = $request->input('order', 'DESC');
        $offset     = $request->input('pagination_offset', 0);
        $limit      = $request->input('limit', 10);
        $status     = $request->input('status', '');
        $startDate  = $request->input('start_date');
        $endDate    = $request->input('end_date');
        $now        = Carbon::now();
        $locale     = app()->getLocale(); // Current app language

        // Start building the base query for banner ads with approved status
        $sliderQuery = Advertisment::where('status', AdvertismentStatus::APPROVED)
            ->where('package_type', AdvertismentPackageType::SPOTLIGHT)

            // Apply search on Arabic/English title if provided
            ->when($search, function ($query) use ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('title_ar', 'like', "%{$search}%")
                        ->orWhere('title_en', 'like', "%{$search}%");
                });
            });

        // Filter by custom status:
        // "1" => Active & paid & currently running
        // "0" => Expired
        if ($status === "1") {
            $sliderQuery->where('end_date', '>', $now)
                ->where('start_date', '<=', $now)
                ->where('is_paid', 1);
        } elseif ($status === "0") {
            $sliderQuery->where('end_date', '<', $now);
        }

        // Filter by date range if both start and end dates are set
        if ($startDate && $endDate) {
            $sliderQuery->whereDate('created_at', '>=', $startDate)
                ->whereDate('created_at', '<=', $endDate);
        }

        // Count total results before applying pagination
        $total = $sliderQuery->toBase()->count();

        // Apply sorting, pagination and get results
        $sliders = $sliderQuery->orderBy($sort, $order)
            ->offset($offset)
            ->limit($limit)
            ->get();

        // Format each slider for the frontend data table
        $data = $sliders->map(function ($s) use ($now, $locale) {
            // Determine badge for status based on time and payment state
            $statusLabel = match (true) {
                $s->end_date < $now => '<label class="badge bg-danger">' . __('admin_labels.expired') . '</label>',
                $s->start_date > $now => '<label class="badge bg-info">' . __('admin_labels.not_started') . '</label>',
                $s->is_paid == 1 => '<label class="badge bg-success">' . __('admin_labels.active') . '</label>',
                default => '<label class="badge bg-warning">' . __('admin_labels.waiting_for_payment') . '</label>',
            };

            // Define URLs for actions
            $delete_url = route('admin.slider.destroy', $s->id);
            $show_url = route('spotlight.show', $s->id);

            // Action buttons dropdown
            $action = '<div class="dropdown bootstrap-table-dropdown">
            <a class="dropdown-item dropdown_menu_items" href="' . $show_url . '"><i class="bx bx-show mx-2"></i></a>
            <a class="dropdown-item delete-data dropdown_menu_items" data-url="' . $delete_url . '"><i class="bx bx-trash mx-2"></i></a>
        </div>';

            // Load the related offer/store name based on ad type
            $typName = '-';
            if ($s->advertisement_type === AdvertismentType::OFFER) {
                $offer = Offer::find($s->offer_id);
                $typName = $offer?->{"title_{$locale}"} ?? '-';
            } elseif ($s->advertisement_type === AdvertismentType::STORE) {
                $store = Store::find($s->store_id);
                $typName = $store?->{"name_{$locale}"} ?? '-';
            }

            // Get image URL and prepare thumbnail lightbox component
            $imageUrl = getMediaImageUrl($s->image_url);
            $imageComponent = view('components.image-lightbox', [
                'id'        => "image-{$s->id}",
                'url'       => $imageUrl,
                'thumbnail' => $imageUrl,
                'width'     => 80,
            ])->render();

            // Return formatted row
            return [
                'id'            => $s->id,
                'status'        => $statusLabel,
                'type'          => $s->advertisement_type->label(),
                'created_by'    => $s->created_by_type,
                'type_name'     => $typName,
                'start_date'    => $s->start_date?->format('d-m-Y'),
                'end_date'      => $s->end_date?->format('d-m-Y'),
                'operate'       => $action
            ];
        });

        // Return JSON response for frontend tables
        return response()->json([
            "rows" => $data,
            "total" => $total,
        ]);
    }

    /* 
    * Show offer/store Spotlight ad page
    */
    public function show($id)
    {
        $ad = Advertisment::with(['store', 'offer.images', 'offer.store', 'store.user','package'])->findOrFail($id);

        $store = $ad->advertisement_type === AdvertismentType::OFFER
            ? $ad->offer?->store
            : $ad->store;

        $offer = $ad->advertisement_type ===  AdvertismentType::OFFER ? $ad->offer : null;
        $creatorUser = $ad->created_by_type === 'STORE'
            ? User::find($ad->created_by_id)
            : null;
            Log::info('offwr', [$creatorUser]);

        return view('admin.pages.views.spotlight', compact('ad', 'store', 'offer', 'creatorUser'));
    }
}
