@extends('admin/layout')
@section('title')
    {{ labels('admin_labels.sliders', 'Sliders') }}
@endsection
@section('content')
    <div class="d-flex row align-items-center" >
        <div class="col-md-6 col-xl-6 page-info-title">
            <h3>{{ labels('admin_labels.sliders', 'Sliders') }}</h3>
            <p class="sub_title">
                {{ labels('admin_labels.boost_sales_with_captivating_and_profitable_promotions', 'Boost Sales with Captivating and Profitable Promotions') }}
            </p>
        </div>
        <div class="col-md-6 col-xl-6 d-flex justify-content-end" dir='ltr'>
            <nav aria-label="breadcrumb" class="float-end">
                <ol class="breadcrumb">
                    <i class='bx bx-home-smile'></i>
                    <li class="breadcrumb-item"><a href="{{ route('admin.home') }}">{{ labels('admin_labels.home', 'Home') }}</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        {{ labels('admin_labels.sliders', 'Sliders') }}</li>
                </ol>
            </nav>
        </div>
    </div>
    {{-- table --}}
    <div class="col-md-12 mt-4 {{  $user_role== 'super_admin' || $logged_in_user->hasPermissionTo('view offer_images') ? '' : 'd-none' }}">
        <section class="overview-data">
            <div class="card content-area p-4 ">
                <div class="row align-items-center d-flex heading mb-5">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-12 col-lg-6">
                                <h4> {{ labels('admin_labels.manage_offers', 'Manage Offers') }}
                                </h4>
                            </div>
                                <div class="col-md-12 col-lg-6 d-flex justify-content-end " dir='ltr'>
                              
                                    <div class="input-group me-2 search-input-grp ">
                                        <span class="search-icon"><i class='bx bx-search-alt'></i></span>
                                        <input type="text" data-table="admin_offer_table"
                                            class="form-control searchInput" placeholder="{{ labels('admin_labels.search', 'Search') }}...">
                                        <span
                                            class="input-group-text">{{ labels('admin_labels.search', 'Search') }}</span>
                                    </div>
                                    <a class="btn me-2" id="tableFilter" data-bs-toggle="offcanvas"
                                        data-bs-target="#columnFilterOffcanvas" data-table="admin_offer_table"
                                        dateFilter='false'  advertismentStatusFilter='false' advertismentPackagesFilter='true'
                                        orderTypeFilter='false'><i class='bx bx-filter-alt'></i></a>
                                    <a class="btn me-2" id="tableRefresh"data-table="admin_offer_table"><i
                                            class='bx bx-refresh'></i></a>
                                    <div class="dropdown">
                                        <a class="btn dropdown-toggle export-btn" type="button"
                                            id="exportOptionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class='bx bx-download'></i>
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="exportOptionsDropdown">
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_offer_table','csv')">CSV</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_offer_table','json')">JSON</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_offer_table','sql')">SQL</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_offer_table','excel')">Excel</button></li>
                                        </ul>
                                    </div>
                                </div>
                        </div>
                    </div>
                </div>
                    <div class="row">
                    
                        <div class="col-md-12">
                            <div class="pt-0">
                                <div class="table-responsive">
                                    <table class='table' id="admin_offer_table" data-toggle="table"
                                        data-loading-template="loadingTemplate" data-url="{{ route('ads.list') }}"
                                        data-click-to-select="true" data-side-pagination="server" data-pagination="true"
                                        data-page-list="[5, 10, 20, 50, 100, 200]" data-search="false"
                                        data-show-columns="false" data-show-refresh="false" data-trim-on-search="false"
                                        data-sort-name="id" data-sort-order="desc" data-mobile-responsive="true"
                                        data-toolbar="" data-show-export="false" data-maintain-selected="true"
                                        data-export-types='["txt","excel"]' data-query-params="queryParams">
                                        <thead>
                                            <tr>
                                            <th data-field="id" data-sortable='true'
                                                        data-footer-formatter="totalFormatter">
                                                        {{ labels('admin_labels.id', 'ID') }}
                                                    </th>
                                                    <th data-field="package_id" data-sortable='true'>
                                                        {{ __('admin_labels.package_id') }}
                                                    </th>
                                                    <th data-field="package_name" data-sortable='true'>
                                                        {{ __('admin_labels.package_name') }}
                                                    </th>
                                                    <th data-field="image" data-sortable='true'
                                                        data-visible="false">
                                                        {{ __('admin_labels.image') }}
                                                    </th>
                                                    <th data-field="title_ar" data-sortable='true'
                                                        data-visible="false">
                                                        {{ __('admin_labels.name_ar') }}
                                                    </th>
                                                    <th data-field="title_en" data-sortable='true'
                                                        data-visible="false">
                                                        {{ __('admin_labels.name_en') }}
                                                    </th>
                                                    <th data-field="description_ar" data-sortable='false'
                                                        data-visible="false">
                                                        {{ __('admin_labels.description_ar') }}
                                                    </th>
                                                    <th data-field="description_en" data-sortable='false'
                                                        data-visible="false">
                                                        {{ __('admin_labels.description_en') }}
                                                    </th>
                                                    <th data-field="advertisement_type" data-sortable='false'>
                                                        {{ __('admin_labels.advertisment_type') }}
                                                    </th>
                                                    <th data-field="start_date" data-sortable='false'>
                                                        {{ __('admin_labels.advertisment_start_date') }}
                                                    </th>
                                                    <th data-field="end_date" data-sortable='false'
                                                        data-visible='false'>
                                                        {{ __('admin_labels.advertisment_end_date') }}
                                                    </th>
                                                    <th data-field="active_status" data-sortable='false'
                                                        data-visible="true">
                                                        {{ __('admin_labels.status') }}
                                                    </th>
                                                    <th data-field="status" data-sortable='false'
                                                        data-visible='false'>
                                                        {{ __('admin_labels.expire_status') }}
                                                    </th>
                                                    <th data-field="created_at" data-sortable='false'
                                                        data-visible='false'>
                                                        {{ __('admin_labels.created_at') }}
                                                    </th>

                                                    <th data-field="operate" data-sortable='false'>
                                                        {{ labels('admin_labels.action', 'Action') }}
                                                    </th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </section>
    </div>
@endsection
