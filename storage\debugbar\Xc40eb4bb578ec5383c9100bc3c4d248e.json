{"__meta": {"id": "Xc40eb4bb578ec5383c9100bc3c4d248e", "datetime": "2025-06-27 19:48:24", "utime": **********.775751, "method": "GET", "uri": "/admin/user/search_seller", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[19:48:24] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\UserController.php on line 331", "message_html": null, "is_string": false, "label": "warning", "time": **********.727171, "xdebug_link": null, "collector": "log"}, {"message": "[19:48:24] LOG.error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'users.active' in 'where clause' (Connection: mysql, SQL: select `users`.`id`, `users`.`username` from `users` inner join `seller_data` on `seller_data`.`user_id` = `users`.`id` where `users`.`username` like %% and `users`.`active` = 1 and `users`.`role_id` = 4 and `seller_data`.`status` = 1) {\n    \"userId\": 402,\n    \"exception\": {\n        \"errorInfo\": [\n            \"42S22\",\n            1054,\n            \"Unknown column 'users.active' in 'where clause'\"\n        ],\n        \"connectionName\": \"mysql\"\n    }\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.747662, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751053700.733404, "end": **********.775819, "duration": 4.042415142059326, "duration_str": "4.04s", "measures": [{"label": "Booting", "start": 1751053700.733404, "relative_start": 0, "end": **********.491767, "relative_end": **********.491767, "duration": 3.7583630084991455, "duration_str": "3.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.4918, "relative_start": 3.7583961486816406, "end": **********.775826, "relative_end": 6.9141387939453125e-06, "duration": 0.2840259075164795, "duration_str": "284ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 29434096, "peak_usage_str": "28MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\Database\\QueryException", "message": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'users.active' in 'where clause' (Connection: mysql, SQL: select `users`.`id`, `users`.`username` from `users` inner join `seller_data` on `seller_data`.`user_id` = `users`.`id` where `users`.`username` like %% and `users`.`active` = 1 and `users`.`role_id` = 4 and `seller_data`.`status` = 1)", "code": "42S22", "file": "vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 829, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-36035057 data-indent-pad=\"  \"><span class=sf-dump-note>array:72</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>783</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">runQueryCallback</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"233 characters\">select `users`.`id`, `users`.`username` from `users` inner join `seller_data` on `seller_data`.`user_id` = `users`.`id` where `users`.`username` like ? and `users`.`active` = ? and `users`.`role_id` = ? and `seller_data`.`status` = ?</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">%%</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>1</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>4</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>414</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"233 characters\">select `users`.`id`, `users`.`username` from `users` inner join `seller_data` on `seller_data`.`user_id` = `users`.`id` where `users`.`username` like ? and `users`.`active` = ? and `users`.`role_id` = ? and `seller_data`.`status` = ?</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">%%</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>1</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>4</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2913</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"233 characters\">select `users`.`id`, `users`.`username` from `users` inner join `seller_data` on `seller_data`.`user_id` = `users`.`id` where `users`.`username` like ? and `users`.`active` = ? and `users`.`role_id` = ? and `seller_data`.`status` = ?</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">%%</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>1</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>4</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2902</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">runSelect</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Database\\Query\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>3456</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Database\\Query\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Database\\Query\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2901</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">onceWithColumns</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Database\\Query\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Database\\Query\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>723</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">getModels</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"45 characters\">app/Http/Controllers/Admin/UserController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>339</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Routing/Controller.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">searchSeller</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"41 characters\">App\\Http\\Controllers\\Admin\\UserController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callAction</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Routing\\Controller</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">searchSeller</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref>#40</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ParameterBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ParameterBag</span> {<a class=sf-dump-ref>#45</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#41</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">query</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#48</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">server</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ServerBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ServerBag</span> {<a class=sf-dump-ref>#43</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:31</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\public</span>\"\n              \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n              \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59385</span>\"\n              \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n              \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n              \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n              \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n              \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/admin/user/search_seller</span>\"\n              \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n              \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n              \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"56 characters\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\public\\index.php</span>\"\n              \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/admin/user/search_seller</span>\"\n              \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"35 characters\">/index.php/admin/user/search_seller</span>\"\n              \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n              \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n              \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n              \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n              \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n              \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/admin/seller_email_notification</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n              \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRYQ2pTMVpqNUtKVkVaYlZYc3Z1SFE9PSIsInZhbHVlIjoiak1DSXFxQVRvTEg5YjNNbVhRdkttYXY1UVErYm5LSHhtd2ZZUFJOYWNpNk5BQXcyendJYXdwQTRKSVk4d1I2akJ0cWRoVXFuY25NUlVlN1RXTkdlcTBjdXpzVlQ2dWQ3TzZYOWhhTTRGUjFuWG5UWDVvUW16YUFScFlBbUxodGgiLCJtYWMiOiIwN2FhZmQzMWZmODRlOTlkZmNlYWYxZDQ1NTYwMDI3YWRkZTliZGVkYTAzYjUzZDdjM2M1OTRhMDIyOWVmZjM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlvTWdMRXc1SnRCdU5EUjROc2pwTWc9PSIsInZhbHVlIjoibUh6YkM4SGlYdVJBcDZEV0tScFBlU0V4b2Uvd1pwZkFSYnVWbXhlT0FFOFJ1ZFg4dzdEUlMyVHl6LzZuMmJhdHRTVm1xamRVakJ4MEc5NjlDVzdnT2ZjUi9MM0ZXYWtrTjczTVpOYjMzd21JNFRrWGVFNzlqN3Yrd08zcHVXTkwiLCJtYWMiOiJjZDQwNDNmZDQ3NDE5YjEzYzNiZGY0MmQzNWNjNzQyNTI3ZTFiM2RjZDcyNjhkZjMxZmIwNDU3MTFhNTM4NDNmIiwidGFnIjoiIn0%3D</span>\"\n              \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751053700.7334</span>\n              \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751053700</span>\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">files</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\FileBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileBag</span> {<a class=sf-dump-ref>#47</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">cookies</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#46</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n              \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pDpd1WaU2b58PtUj5UD1AlTYICdgMgvf3egMuLWL</span>\"\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\HeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HeaderBag</span> {<a class=sf-dump-ref>#42</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/admin/seller_email_notification</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRYQ2pTMVpqNUtKVkVaYlZYc3Z1SFE9PSIsInZhbHVlIjoiak1DSXFxQVRvTEg5YjNNbVhRdkttYXY1UVErYm5LSHhtd2ZZUFJOYWNpNk5BQXcyendJYXdwQTRKSVk4d1I2akJ0cWRoVXFuY25NUlVlN1RXTkdlcTBjdXpzVlQ2dWQ3TzZYOWhhTTRGUjFuWG5UWDVvUW16YUFScFlBbUxodGgiLCJtYWMiOiIwN2FhZmQzMWZmODRlOTlkZmNlYWYxZDQ1NTYwMDI3YWRkZTliZGVkYTAzYjUzZDdjM2M1OTRhMDIyOWVmZjM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlvTWdMRXc1SnRCdU5EUjROc2pwTWc9PSIsInZhbHVlIjoibUh6YkM4SGlYdVJBcDZEV0tScFBlU0V4b2Uvd1pwZkFSYnVWbXhlT0FFOFJ1ZFg4dzdEUlMyVHl6LzZuMmJhdHRTVm1xamRVakJ4MEc5NjlDVzdnT2ZjUi9MM0ZXYWtrTjczTVpOYjMzd21JNFRrWGVFNzlqN3Yrd08zcHVXTkwiLCJtYWMiOiJjZDQwNDNmZDQ3NDE5YjEzYzNiZGY0MmQzNWNjNzQyNTI3ZTFiM2RjZDcyNjhkZjMxZmIwNDU3MTFhNTM4NDNmIiwidGFnIjoiIn0%3D</span>\"\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">content</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">languages</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">charsets</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">encodings</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">acceptableContentTypes</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">text/javascript</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">pathInfo</span>: \"<span class=sf-dump-str title=\"25 characters\">/admin/user/search_seller</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">requestUri</span>: \"<span class=sf-dump-str title=\"25 characters\">/admin/user/search_seller</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">baseUrl</span>: \"\"\n          #<span class=sf-dump-protected title=\"Protected property\">basePath</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">method</span>: \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">format</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=sf-dump-note title=\"Illuminate\\Session\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Store</span> {<a class=sf-dump-ref>#1692</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">id</span>: \"<span class=sf-dump-str title=\"40 characters\">pDpd1WaU2b58PtUj5UD1AlTYICdgMgvf3egMuLWL</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"15 characters\">laravel_session</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n              \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n              \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>20</span>\n              \"<span class=sf-dump-key>store_name</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>store_image</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n              \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/admin/seller_email_notification</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>old</span>\" => []\n                \"<span class=sf-dump-key>new</span>\" => []\n              </samp>]\n              \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>402</span>\n              \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"1756 characters\">{&quot;app_name&quot;:&quot;Awfarly&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:&quot;\\/media\\/logo-awfarly-1747828089_8299.png&quot;,&quot;favicon&quot;:&quot;\\/media\\/web-logo-1736311270_9133.svg&quot;,&quot;on_boarding_image&quot;:&quot;&quot;,&quot;on_boarding_video&quot;:&quot;&quot;,&quot;storage_type&quot;:&quot;local&quot;,&quot;on_boarding_media_type&quot;:&quot;image&quot;,&quot;current_version_of_android_app&quot;:&quot;1.0.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;current_version_of_android_app_for_seller&quot;:&quot;1.0.0&quot;,&quot;current_version_of_ios_app_for_seller&quot;:&quot;1.0.0&quot;,&quot;current_version_of_android_app_for_delivery_boy&quot;:&quot;1.0.0&quot;,&quot;current_version_of_ios_app_for_delivery_boy&quot;:&quot;1.0.0&quot;,&quot;order_delivery_otp_system&quot;:1,&quot;system_timezone&quot;:&quot;Asia\\/Kolkata&quot;,&quot;minimum_cart_amount&quot;:&quot;10&quot;,&quot;maximum_item_allowed_in_cart&quot;:&quot;10&quot;,&quot;low_stock_limit&quot;:&quot;7&quot;,&quot;max_days_to_return_item&quot;:&quot;1&quot;,&quot;delivery_boy_bonus&quot;:&quot;11&quot;,&quot;enable_cart_button_on_product_list_view&quot;:1,&quot;version_system_status&quot;:1,&quot;expand_product_image&quot;:1,&quot;tax_name&quot;:&quot;GST&quot;,&quot;tax_number&quot;:&quot;123456&quot;,&quot;google&quot;:1,&quot;facebook&quot;:1,&quot;apple&quot;:1,&quot;refer_and_earn_status&quot;:1,&quot;minimum_refer_and_earn_amount&quot;:&quot;10&quot;,&quot;minimum_refer_and_earn_bonus&quot;:&quot;10&quot;,&quot;refer_and_earn_method&quot;:&quot;percentage&quot;,&quot;max_refer_and_earn_amount&quot;:&quot;10&quot;,&quot;number_of_times_bonus_given_to_customer&quot;:&quot;1&quot;,&quot;wallet_balance_status&quot;:1,&quot;wallet_balance_amount&quot;:&quot;9999&quot;,&quot;authentication_method&quot;:&quot;firebase&quot;,&quot;store_currency&quot;:null,&quot;single_seller_order_system&quot;:0,&quot;customer_app_maintenance_status&quot;:0,&quot;seller_app_maintenance_status&quot;:0,&quot;delivery_boy_app_maintenance_status&quot;:0,&quot;message_for_customer_app&quot;:null,&quot;message_for_seller_app&quot;:&quot;test testtesttesttes tt esttesttesttestt esttesttesttesttes  ttesttest  testtesttesttesttes ttesttesttesttesttesttesttest&quot;,&quot;message_for_delivery_boy_app&quot;:&quot;test&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0,&quot;currency_setting&quot;:&quot;\\u062f.\\u0644&quot;}</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">handler</span>: <span class=sf-dump-note title=\"Illuminate\\Session\\FileSessionHandler\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileSessionHandler</span> {<a class=sf-dump-ref>#1693</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">files</span>: <span class=sf-dump-note title=\"Illuminate\\Filesystem\\Filesystem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Filesystem</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Filesystem</span> {<a class=sf-dump-ref>#230</a>}\n              #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"66 characters\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\storage\\framework/sessions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">minutes</span>: \"<span class=sf-dump-str title=\"3 characters\">120</span>\"\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">serialization</span>: \"<span class=sf-dump-str title=\"3 characters\">php</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">started</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">locale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">preferredFormat</span>: <span class=sf-dump-const>null</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isHostValid</span>: <span class=sf-dump-const>true</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isForwardedValid</span>: <span class=sf-dump-const>true</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isSafeContentPreferred</span>: <span class=sf-dump-const title=\"Uninitialized property\">? bool</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">trustedValuesCache</span>: []\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isIisRewrite</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">json</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">convertedFiles</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">userResolver</span>: <span class=sf-dump-note>Closure($guard = null)</span> {<a class=sf-dump-ref>#2136</a><samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Auth\\AuthServiceProvider\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>AuthServiceProvider</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Auth\\AuthServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>AuthServiceProvider</span> {<a class=sf-dump-ref>#209</a> &#8230;}\n            <span class=sf-dump-meta>use</span>: {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>$app</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref>#2</a> &#8230;}\n            </samp>}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php\n108 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Auth\\AuthServiceProvider.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">88 to 90</span>\"\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">routeResolver</span>: <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#2143</a><samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Router\n25 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Router</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router</span> {<a class=sf-dump-ref>#30</a> &#8230;}\n            <span class=sf-dump-meta>use</span>: {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>$route</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref>#466</a> &#8230;}\n            </samp>}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php\n98 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">779 to 779</span>\"\n          </samp>}\n          <span class=sf-dump-meta>basePath</span>: \"\"\n          <span class=sf-dump-meta>format</span>: \"<span class=sf-dump-str title=\"4 characters\">html</span>\"\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>259</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"50 characters\">[object App\\Http\\Controllers\\Admin\\UserController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">searchSeller</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>205</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>806</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"42 characters\">app/Http/Middleware/CheckStoreNotEmpty.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>29</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Http\\Middleware\\CheckStoreNotEmpty</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">app/Http/Middleware/RoleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\RoleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">super_admin</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">editor</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"41 characters\">app/Http/Middleware/CheckInstallation.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Http\\Middleware\\CheckInstallation</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">app/Http/Middleware/LogoutMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>10</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">App\\Http\\Middleware\\LogoutMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"39 characters\">app/Http/Middleware/SetDefaultStore.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>57</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Http\\Middleware\\SetDefaultStore</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">app/Http/Middleware/GetDefaultData.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>18</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\GetDefaultData</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>57</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"39 characters\">app/Http/Middleware/LanguageManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>24</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Http\\Middleware\\LanguageManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>784</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>748</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>737</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>99</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-36035057\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                );\n", "            }\n", "\n", "            throw new QueryException(\n", "                $this->getName(), $query, $this->prepareBindings($bindings), $e\n", "            );\n", "        }\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FConnection.php&line=829", "ajax": false, "filename": "Connection.php", "line": "829"}}, {"type": "PDOException", "message": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'users.active' in 'where clause'", "code": "42S22", "file": "vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 423, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-2111294097 data-indent-pad=\"  \"><span class=sf-dump-note>array:74</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>423</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">prepare</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PDO</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"233 characters\">select `users`.`id`, `users`.`username` from `users` inner join `seller_data` on `seller_data`.`user_id` = `users`.`id` where `users`.`username` like ? and `users`.`active` = ? and `users`.`role_id` = ? and `seller_data`.`status` = ?</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>816</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Database\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"233 characters\">select `users`.`id`, `users`.`username` from `users` inner join `seller_data` on `seller_data`.`user_id` = `users`.`id` where `users`.`username` like ? and `users`.`active` = ? and `users`.`role_id` = ? and `seller_data`.`status` = ?</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">%%</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>1</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>4</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>783</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">runQueryCallback</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"233 characters\">select `users`.`id`, `users`.`username` from `users` inner join `seller_data` on `seller_data`.`user_id` = `users`.`id` where `users`.`username` like ? and `users`.`active` = ? and `users`.`role_id` = ? and `seller_data`.`status` = ?</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">%%</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>1</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>4</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>414</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"233 characters\">select `users`.`id`, `users`.`username` from `users` inner join `seller_data` on `seller_data`.`user_id` = `users`.`id` where `users`.`username` like ? and `users`.`active` = ? and `users`.`role_id` = ? and `seller_data`.`status` = ?</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">%%</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>1</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>4</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2913</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"233 characters\">select `users`.`id`, `users`.`username` from `users` inner join `seller_data` on `seller_data`.`user_id` = `users`.`id` where `users`.`username` like ? and `users`.`active` = ? and `users`.`role_id` = ? and `seller_data`.`status` = ?</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">%%</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>1</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>4</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2902</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">runSelect</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Database\\Query\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>3456</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Illuminate\\Database\\Query\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Database\\Query\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2901</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">onceWithColumns</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Database\\Query\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Database\\Query\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>723</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">getModels</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"45 characters\">app/Http/Controllers/Admin/UserController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>339</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Routing/Controller.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">searchSeller</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"41 characters\">App\\Http\\Controllers\\Admin\\UserController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callAction</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Routing\\Controller</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">searchSeller</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref>#40</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ParameterBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ParameterBag</span> {<a class=sf-dump-ref>#45</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#41</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">query</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#48</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">server</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ServerBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ServerBag</span> {<a class=sf-dump-ref>#43</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:31</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\public</span>\"\n              \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n              \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59385</span>\"\n              \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n              \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n              \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n              \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n              \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/admin/user/search_seller</span>\"\n              \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n              \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n              \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"56 characters\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\public\\index.php</span>\"\n              \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/admin/user/search_seller</span>\"\n              \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"35 characters\">/index.php/admin/user/search_seller</span>\"\n              \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n              \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n              \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n              \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n              \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n              \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/admin/seller_email_notification</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n              \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRYQ2pTMVpqNUtKVkVaYlZYc3Z1SFE9PSIsInZhbHVlIjoiak1DSXFxQVRvTEg5YjNNbVhRdkttYXY1UVErYm5LSHhtd2ZZUFJOYWNpNk5BQXcyendJYXdwQTRKSVk4d1I2akJ0cWRoVXFuY25NUlVlN1RXTkdlcTBjdXpzVlQ2dWQ3TzZYOWhhTTRGUjFuWG5UWDVvUW16YUFScFlBbUxodGgiLCJtYWMiOiIwN2FhZmQzMWZmODRlOTlkZmNlYWYxZDQ1NTYwMDI3YWRkZTliZGVkYTAzYjUzZDdjM2M1OTRhMDIyOWVmZjM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlvTWdMRXc1SnRCdU5EUjROc2pwTWc9PSIsInZhbHVlIjoibUh6YkM4SGlYdVJBcDZEV0tScFBlU0V4b2Uvd1pwZkFSYnVWbXhlT0FFOFJ1ZFg4dzdEUlMyVHl6LzZuMmJhdHRTVm1xamRVakJ4MEc5NjlDVzdnT2ZjUi9MM0ZXYWtrTjczTVpOYjMzd21JNFRrWGVFNzlqN3Yrd08zcHVXTkwiLCJtYWMiOiJjZDQwNDNmZDQ3NDE5YjEzYzNiZGY0MmQzNWNjNzQyNTI3ZTFiM2RjZDcyNjhkZjMxZmIwNDU3MTFhNTM4NDNmIiwidGFnIjoiIn0%3D</span>\"\n              \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751053700.7334</span>\n              \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751053700</span>\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">files</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\FileBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileBag</span> {<a class=sf-dump-ref>#47</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">cookies</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#46</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n              \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pDpd1WaU2b58PtUj5UD1AlTYICdgMgvf3egMuLWL</span>\"\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\HeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HeaderBag</span> {<a class=sf-dump-ref>#42</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/admin/seller_email_notification</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRYQ2pTMVpqNUtKVkVaYlZYc3Z1SFE9PSIsInZhbHVlIjoiak1DSXFxQVRvTEg5YjNNbVhRdkttYXY1UVErYm5LSHhtd2ZZUFJOYWNpNk5BQXcyendJYXdwQTRKSVk4d1I2akJ0cWRoVXFuY25NUlVlN1RXTkdlcTBjdXpzVlQ2dWQ3TzZYOWhhTTRGUjFuWG5UWDVvUW16YUFScFlBbUxodGgiLCJtYWMiOiIwN2FhZmQzMWZmODRlOTlkZmNlYWYxZDQ1NTYwMDI3YWRkZTliZGVkYTAzYjUzZDdjM2M1OTRhMDIyOWVmZjM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlvTWdMRXc1SnRCdU5EUjROc2pwTWc9PSIsInZhbHVlIjoibUh6YkM4SGlYdVJBcDZEV0tScFBlU0V4b2Uvd1pwZkFSYnVWbXhlT0FFOFJ1ZFg4dzdEUlMyVHl6LzZuMmJhdHRTVm1xamRVakJ4MEc5NjlDVzdnT2ZjUi9MM0ZXYWtrTjczTVpOYjMzd21JNFRrWGVFNzlqN3Yrd08zcHVXTkwiLCJtYWMiOiJjZDQwNDNmZDQ3NDE5YjEzYzNiZGY0MmQzNWNjNzQyNTI3ZTFiM2RjZDcyNjhkZjMxZmIwNDU3MTFhNTM4NDNmIiwidGFnIjoiIn0%3D</span>\"\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">content</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">languages</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">charsets</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">encodings</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">acceptableContentTypes</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">text/javascript</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">pathInfo</span>: \"<span class=sf-dump-str title=\"25 characters\">/admin/user/search_seller</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">requestUri</span>: \"<span class=sf-dump-str title=\"25 characters\">/admin/user/search_seller</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">baseUrl</span>: \"\"\n          #<span class=sf-dump-protected title=\"Protected property\">basePath</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">method</span>: \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">format</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=sf-dump-note title=\"Illuminate\\Session\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Store</span> {<a class=sf-dump-ref>#1692</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">id</span>: \"<span class=sf-dump-str title=\"40 characters\">pDpd1WaU2b58PtUj5UD1AlTYICdgMgvf3egMuLWL</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"15 characters\">laravel_session</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n              \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n              \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>20</span>\n              \"<span class=sf-dump-key>store_name</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>store_image</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n              \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/admin/seller_email_notification</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>old</span>\" => []\n                \"<span class=sf-dump-key>new</span>\" => []\n              </samp>]\n              \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>402</span>\n              \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"1756 characters\">{&quot;app_name&quot;:&quot;Awfarly&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:&quot;\\/media\\/logo-awfarly-1747828089_8299.png&quot;,&quot;favicon&quot;:&quot;\\/media\\/web-logo-1736311270_9133.svg&quot;,&quot;on_boarding_image&quot;:&quot;&quot;,&quot;on_boarding_video&quot;:&quot;&quot;,&quot;storage_type&quot;:&quot;local&quot;,&quot;on_boarding_media_type&quot;:&quot;image&quot;,&quot;current_version_of_android_app&quot;:&quot;1.0.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;current_version_of_android_app_for_seller&quot;:&quot;1.0.0&quot;,&quot;current_version_of_ios_app_for_seller&quot;:&quot;1.0.0&quot;,&quot;current_version_of_android_app_for_delivery_boy&quot;:&quot;1.0.0&quot;,&quot;current_version_of_ios_app_for_delivery_boy&quot;:&quot;1.0.0&quot;,&quot;order_delivery_otp_system&quot;:1,&quot;system_timezone&quot;:&quot;Asia\\/Kolkata&quot;,&quot;minimum_cart_amount&quot;:&quot;10&quot;,&quot;maximum_item_allowed_in_cart&quot;:&quot;10&quot;,&quot;low_stock_limit&quot;:&quot;7&quot;,&quot;max_days_to_return_item&quot;:&quot;1&quot;,&quot;delivery_boy_bonus&quot;:&quot;11&quot;,&quot;enable_cart_button_on_product_list_view&quot;:1,&quot;version_system_status&quot;:1,&quot;expand_product_image&quot;:1,&quot;tax_name&quot;:&quot;GST&quot;,&quot;tax_number&quot;:&quot;123456&quot;,&quot;google&quot;:1,&quot;facebook&quot;:1,&quot;apple&quot;:1,&quot;refer_and_earn_status&quot;:1,&quot;minimum_refer_and_earn_amount&quot;:&quot;10&quot;,&quot;minimum_refer_and_earn_bonus&quot;:&quot;10&quot;,&quot;refer_and_earn_method&quot;:&quot;percentage&quot;,&quot;max_refer_and_earn_amount&quot;:&quot;10&quot;,&quot;number_of_times_bonus_given_to_customer&quot;:&quot;1&quot;,&quot;wallet_balance_status&quot;:1,&quot;wallet_balance_amount&quot;:&quot;9999&quot;,&quot;authentication_method&quot;:&quot;firebase&quot;,&quot;store_currency&quot;:null,&quot;single_seller_order_system&quot;:0,&quot;customer_app_maintenance_status&quot;:0,&quot;seller_app_maintenance_status&quot;:0,&quot;delivery_boy_app_maintenance_status&quot;:0,&quot;message_for_customer_app&quot;:null,&quot;message_for_seller_app&quot;:&quot;test testtesttesttes tt esttesttesttestt esttesttesttesttes  ttesttest  testtesttesttesttes ttesttesttesttesttesttesttest&quot;,&quot;message_for_delivery_boy_app&quot;:&quot;test&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0,&quot;currency_setting&quot;:&quot;\\u062f.\\u0644&quot;}</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">handler</span>: <span class=sf-dump-note title=\"Illuminate\\Session\\FileSessionHandler\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileSessionHandler</span> {<a class=sf-dump-ref>#1693</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">files</span>: <span class=sf-dump-note title=\"Illuminate\\Filesystem\\Filesystem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Filesystem</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Filesystem</span> {<a class=sf-dump-ref>#230</a>}\n              #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"66 characters\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\storage\\framework/sessions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">minutes</span>: \"<span class=sf-dump-str title=\"3 characters\">120</span>\"\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">serialization</span>: \"<span class=sf-dump-str title=\"3 characters\">php</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">started</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">locale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">preferredFormat</span>: <span class=sf-dump-const>null</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isHostValid</span>: <span class=sf-dump-const>true</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isForwardedValid</span>: <span class=sf-dump-const>true</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isSafeContentPreferred</span>: <span class=sf-dump-const title=\"Uninitialized property\">? bool</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">trustedValuesCache</span>: []\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isIisRewrite</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">json</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">convertedFiles</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">userResolver</span>: <span class=sf-dump-note>Closure($guard = null)</span> {<a class=sf-dump-ref>#2136</a><samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Auth\\AuthServiceProvider\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>AuthServiceProvider</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Auth\\AuthServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>AuthServiceProvider</span> {<a class=sf-dump-ref>#209</a> &#8230;}\n            <span class=sf-dump-meta>use</span>: {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>$app</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref>#2</a> &#8230;}\n            </samp>}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php\n108 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Auth\\AuthServiceProvider.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">88 to 90</span>\"\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">routeResolver</span>: <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#2143</a><samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Router\n25 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Router</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router</span> {<a class=sf-dump-ref>#30</a> &#8230;}\n            <span class=sf-dump-meta>use</span>: {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>$route</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref>#466</a> &#8230;}\n            </samp>}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php\n98 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">779 to 779</span>\"\n          </samp>}\n          <span class=sf-dump-meta>basePath</span>: \"\"\n          <span class=sf-dump-meta>format</span>: \"<span class=sf-dump-str title=\"4 characters\">html</span>\"\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>259</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"50 characters\">[object App\\Http\\Controllers\\Admin\\UserController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">searchSeller</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>205</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>806</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"42 characters\">app/Http/Middleware/CheckStoreNotEmpty.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>29</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Http\\Middleware\\CheckStoreNotEmpty</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">app/Http/Middleware/RoleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\RoleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">super_admin</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">editor</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"41 characters\">app/Http/Middleware/CheckInstallation.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Http\\Middleware\\CheckInstallation</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">app/Http/Middleware/LogoutMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>10</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">App\\Http\\Middleware\\LogoutMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"39 characters\">app/Http/Middleware/SetDefaultStore.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>57</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Http\\Middleware\\SetDefaultStore</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">app/Http/Middleware/GetDefaultData.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>18</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\GetDefaultData</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>57</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"39 characters\">app/Http/Middleware/LanguageManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>24</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Http\\Middleware\\LanguageManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>784</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>748</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>737</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>99</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111294097\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            // of the database result set. Each element in the array will be a single\n", "            // row from the database table, and will either be an array or objects.\n", "            $statement = $this->prepared(\n", "                $this->getPdoForSelect($useReadPdo)->prepare($query)\n", "            );\n", "\n", "            $this->bindValues($statement, $this->prepareBindings($bindings));\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FConnection.php&line=423", "ajax": false, "filename": "Connection.php", "line": "423"}}]}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/user/search_seller", "middleware": "web, CheckInstallation, auth, role:super_admin,admin,editor, CheckStoreNotEmpty", "controller": "App\\Http\\Controllers\\Admin\\UserController@searchSeller", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserController.php&line=329\" onclick=\"\">app/Http/Controllers/Admin/UserController.php:329-350</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01003, "accumulated_duration_str": "10.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.6252608, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 21.535}, {"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.653677, "duration": 0.00276, "duration_str": "2.76ms", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 21.535, "width_percent": 27.517}, {"sql": "select * from `users` where `users`.`id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.675638, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 49.053, "width_percent": 20.738}, {"sql": "select * from `roles` where `roles`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.706111, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 69.791, "width_percent": 16.351}, {"sql": "select count(*) as aggregate from `stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "CheckStoreNotEmpty", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckStoreNotEmpty.php", "line": 19}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}], "start": **********.715603, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "CheckStoreNotEmpty:19", "source": {"index": 19, "namespace": "middleware", "name": "CheckStoreNotEmpty", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckStoreNotEmpty.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FCheckStoreNotEmpty.php&line=19", "ajax": false, "filename": "CheckStoreNotEmpty.php", "line": "19"}, "connection": "eshop", "explain": null, "start_percent": 86.142, "width_percent": 13.858}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke", "store_id": "20", "store_name": "null", "store_image": "null", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/seller_email_notification\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "402", "system_settings": "{\"app_name\":\"Awfarly\",\"support_number\":\"919876543210\",\"support_email\":\"<EMAIL>\",\"logo\":\"\\/media\\/logo-awfarly-1747828089_8299.png\",\"favicon\":\"\\/media\\/web-logo-1736311270_9133.svg\",\"on_boarding_image\":\"\",\"on_boarding_video\":\"\",\"storage_type\":\"local\",\"on_boarding_media_type\":\"image\",\"current_version_of_android_app\":\"1.0.0\",\"current_version_of_ios_app\":\"1.0.0\",\"current_version_of_android_app_for_seller\":\"1.0.0\",\"current_version_of_ios_app_for_seller\":\"1.0.0\",\"current_version_of_android_app_for_delivery_boy\":\"1.0.0\",\"current_version_of_ios_app_for_delivery_boy\":\"1.0.0\",\"order_delivery_otp_system\":1,\"system_timezone\":\"Asia\\/Kolkata\",\"minimum_cart_amount\":\"10\",\"maximum_item_allowed_in_cart\":\"10\",\"low_stock_limit\":\"7\",\"max_days_to_return_item\":\"1\",\"delivery_boy_bonus\":\"11\",\"enable_cart_button_on_product_list_view\":1,\"version_system_status\":1,\"expand_product_image\":1,\"tax_name\":\"GST\",\"tax_number\":\"123456\",\"google\":1,\"facebook\":1,\"apple\":1,\"refer_and_earn_status\":1,\"minimum_refer_and_earn_amount\":\"10\",\"minimum_refer_and_earn_bonus\":\"10\",\"refer_and_earn_method\":\"percentage\",\"max_refer_and_earn_amount\":\"10\",\"number_of_times_bonus_given_to_customer\":\"1\",\"wallet_balance_status\":1,\"wallet_balance_amount\":\"9999\",\"authentication_method\":\"firebase\",\"store_currency\":null,\"single_seller_order_system\":0,\"customer_app_maintenance_status\":0,\"seller_app_maintenance_status\":0,\"delivery_boy_app_maintenance_status\":0,\"message_for_customer_app\":null,\"message_for_seller_app\":\"test testtesttesttes tt esttesttesttestt esttesttesttesttes  ttesttest  testtesttesttesttes ttesttesttesttesttesttesttest\",\"message_for_delivery_boy_app\":\"test\",\"sidebar_color\":null,\"sidebar_type\":null,\"navbar_fixed\":0,\"theme_mode\":0,\"currency_setting\":\"\\u062f.\\u0644\"}"}, "request": {"path_info": "/admin/user/search_seller", "status_code": "<pre class=sf-dump id=sf-dump-330514509 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-330514509\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1129575182 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1129575182\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-891614061 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-891614061\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1114054817 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/admin/seller_email_notification</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRYQ2pTMVpqNUtKVkVaYlZYc3Z1SFE9PSIsInZhbHVlIjoiak1DSXFxQVRvTEg5YjNNbVhRdkttYXY1UVErYm5LSHhtd2ZZUFJOYWNpNk5BQXcyendJYXdwQTRKSVk4d1I2akJ0cWRoVXFuY25NUlVlN1RXTkdlcTBjdXpzVlQ2dWQ3TzZYOWhhTTRGUjFuWG5UWDVvUW16YUFScFlBbUxodGgiLCJtYWMiOiIwN2FhZmQzMWZmODRlOTlkZmNlYWYxZDQ1NTYwMDI3YWRkZTliZGVkYTAzYjUzZDdjM2M1OTRhMDIyOWVmZjM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlvTWdMRXc1SnRCdU5EUjROc2pwTWc9PSIsInZhbHVlIjoibUh6YkM4SGlYdVJBcDZEV0tScFBlU0V4b2Uvd1pwZkFSYnVWbXhlT0FFOFJ1ZFg4dzdEUlMyVHl6LzZuMmJhdHRTVm1xamRVakJ4MEc5NjlDVzdnT2ZjUi9MM0ZXYWtrTjczTVpOYjMzd21JNFRrWGVFNzlqN3Yrd08zcHVXTkwiLCJtYWMiOiJjZDQwNDNmZDQ3NDE5YjEzYzNiZGY0MmQzNWNjNzQyNTI3ZTFiM2RjZDcyNjhkZjMxZmIwNDU3MTFhNTM4NDNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114054817\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1900785589 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pDpd1WaU2b58PtUj5UD1AlTYICdgMgvf3egMuLWL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900785589\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-312689573 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 19:48:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik9GNzRYb1U1cHNmQmd6OU1tSGIwakE9PSIsInZhbHVlIjoiNkZzQzdsaWlHRWVyUy81MzRFTkR3OTJFcFFjNFl3YjZUQUliOHhQUXpoOHl5ZnhzSk56YzRXRmZWTGRYcFVLOElncmlBUjdkdDB6cmtIOHB1WW92M2taMEFzOTlBUmZ3SC80QmF0QnRoNk9BdXNFVXVaRHZCVitKcStxOVNZZ1QiLCJtYWMiOiI2MTEyYjM0MGQ2Zjk5ZjY4ZGMxNmZjZGZhMjJiZDlmZTUxMTMzY2NmNGY5NGJjYmYwMTVlNDdiYTA2NDZjNzI0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 21:48:24 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im5uY1dkYTgxazVLVFI0cElsenpwR3c9PSIsInZhbHVlIjoiUDNXZjBOcHVPaFNRS2ZVemw5UHpvckFOZDFQMCt5Q05QTGMxeFN2SXFFaGdncWpSaEl0SFdzTlJJeE1FQ1JOMTBvWjBtd2dkb0RtK1drbHJ0V2k2cmphVmtyVnZTZ3BUbkUreDc3ZDhOMGRKNUZ4c2Zqbmd2YlA3bTdDWmlFRGIiLCJtYWMiOiI3NDg0MGVkMTIzNTc2YmEyZGQ2ZGI2YzgzYWFlYWNjOWE0OWVkNWIyMmQyZTFiMjY4ZjIzMjBjYzQ0YjRjNjY3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 21:48:24 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik9GNzRYb1U1cHNmQmd6OU1tSGIwakE9PSIsInZhbHVlIjoiNkZzQzdsaWlHRWVyUy81MzRFTkR3OTJFcFFjNFl3YjZUQUliOHhQUXpoOHl5ZnhzSk56YzRXRmZWTGRYcFVLOElncmlBUjdkdDB6cmtIOHB1WW92M2taMEFzOTlBUmZ3SC80QmF0QnRoNk9BdXNFVXVaRHZCVitKcStxOVNZZ1QiLCJtYWMiOiI2MTEyYjM0MGQ2Zjk5ZjY4ZGMxNmZjZGZhMjJiZDlmZTUxMTMzY2NmNGY5NGJjYmYwMTVlNDdiYTA2NDZjNzI0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 21:48:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im5uY1dkYTgxazVLVFI0cElsenpwR3c9PSIsInZhbHVlIjoiUDNXZjBOcHVPaFNRS2ZVemw5UHpvckFOZDFQMCt5Q05QTGMxeFN2SXFFaGdncWpSaEl0SFdzTlJJeE1FQ1JOMTBvWjBtd2dkb0RtK1drbHJ0V2k2cmphVmtyVnZTZ3BUbkUreDc3ZDhOMGRKNUZ4c2Zqbmd2YlA3bTdDWmlFRGIiLCJtYWMiOiI3NDg0MGVkMTIzNTc2YmEyZGQ2ZGI2YzgzYWFlYWNjOWE0OWVkNWIyMmQyZTFiMjY4ZjIzMjBjYzQ0YjRjNjY3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 21:48:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-312689573\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1513892491 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0W47EJP6jawIpIroTwVgDu66L6nkO5uUiImSyke</span>\"\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>20</span>\n  \"<span class=sf-dump-key>store_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/admin/seller_email_notification</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>402</span>\n  \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"1756 characters\">{&quot;app_name&quot;:&quot;Awfarly&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:&quot;\\/media\\/logo-awfarly-1747828089_8299.png&quot;,&quot;favicon&quot;:&quot;\\/media\\/web-logo-1736311270_9133.svg&quot;,&quot;on_boarding_image&quot;:&quot;&quot;,&quot;on_boarding_video&quot;:&quot;&quot;,&quot;storage_type&quot;:&quot;local&quot;,&quot;on_boarding_media_type&quot;:&quot;image&quot;,&quot;current_version_of_android_app&quot;:&quot;1.0.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;current_version_of_android_app_for_seller&quot;:&quot;1.0.0&quot;,&quot;current_version_of_ios_app_for_seller&quot;:&quot;1.0.0&quot;,&quot;current_version_of_android_app_for_delivery_boy&quot;:&quot;1.0.0&quot;,&quot;current_version_of_ios_app_for_delivery_boy&quot;:&quot;1.0.0&quot;,&quot;order_delivery_otp_system&quot;:1,&quot;system_timezone&quot;:&quot;Asia\\/Kolkata&quot;,&quot;minimum_cart_amount&quot;:&quot;10&quot;,&quot;maximum_item_allowed_in_cart&quot;:&quot;10&quot;,&quot;low_stock_limit&quot;:&quot;7&quot;,&quot;max_days_to_return_item&quot;:&quot;1&quot;,&quot;delivery_boy_bonus&quot;:&quot;11&quot;,&quot;enable_cart_button_on_product_list_view&quot;:1,&quot;version_system_status&quot;:1,&quot;expand_product_image&quot;:1,&quot;tax_name&quot;:&quot;GST&quot;,&quot;tax_number&quot;:&quot;123456&quot;,&quot;google&quot;:1,&quot;facebook&quot;:1,&quot;apple&quot;:1,&quot;refer_and_earn_status&quot;:1,&quot;minimum_refer_and_earn_amount&quot;:&quot;10&quot;,&quot;minimum_refer_and_earn_bonus&quot;:&quot;10&quot;,&quot;refer_and_earn_method&quot;:&quot;percentage&quot;,&quot;max_refer_and_earn_amount&quot;:&quot;10&quot;,&quot;number_of_times_bonus_given_to_customer&quot;:&quot;1&quot;,&quot;wallet_balance_status&quot;:1,&quot;wallet_balance_amount&quot;:&quot;9999&quot;,&quot;authentication_method&quot;:&quot;firebase&quot;,&quot;store_currency&quot;:null,&quot;single_seller_order_system&quot;:0,&quot;customer_app_maintenance_status&quot;:0,&quot;seller_app_maintenance_status&quot;:0,&quot;delivery_boy_app_maintenance_status&quot;:0,&quot;message_for_customer_app&quot;:null,&quot;message_for_seller_app&quot;:&quot;test testtesttesttes tt esttesttesttestt esttesttesttesttes  ttesttest  testtesttesttesttes ttesttesttesttesttesttesttest&quot;,&quot;message_for_delivery_boy_app&quot;:&quot;test&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0,&quot;currency_setting&quot;:&quot;\\u062f.\\u0644&quot;}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513892491\", {\"maxDepth\":0})</script>\n"}}