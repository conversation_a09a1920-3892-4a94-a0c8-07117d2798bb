<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddCityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name_ar' => 'required|max:255',
            'name_en' => 'required|max:255',
        
        ];
    }
 
    public function messages(): array
{
    return [
        'name_ar.required' => __('admin_labels.name_ar_required'),
        'name_en.required' => __('admin_labels.name_en_required'),
      
    ];
}

}
