

<?php $__env->startSection('title', __('admin_labels.requested_ads')); ?>

<?php $__env->startSection('content'); ?>
<?php if(session('success')): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="bx bx-check-circle me-2"></i> <?php echo e(session('success')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>
<?php if(session('error')): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="bx bx-error-circle me-2"></i> <?php echo e(session('error')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="container py-4">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom-0 py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="bx bx-list-ul me-2"></i>
                    <?php echo e(__('admin_labels.requested_ads')); ?>

                </h5>

            </div>
        </div>

        <div class="card-body">
            <div class="row g-4">
                <?php $__empty_1 = true; $__currentLoopData = $ads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-12">
                    <div class="card border-0 shadow-sm hover-lift">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-start gap-3">
                                <!-- Image Column -->
                                <?php if($ad->image): ?>
                                <div class="flex-shrink-0">
                                    <img src="<?php echo e(getMediaImageUrl($ad->image_url)); ?>"
                                        class="rounded-2 border"
                                        style="width: 120px; height: 90px; object-fit: cover;">
                                </div>
                                <?php endif; ?>

                                <!-- Content Column -->
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1 fw-semibold"><?php echo e($ad->title_ar); ?></h6>
                                            <p class="small text-muted mb-2"><?php echo e($ad->description_ar ?? $ad->description_ar); ?></p>

                                            <div class="d-flex flex-wrap gap-3">
                                                <span class="badge bg-primary bg-opacity-10 text">
                                                    <?php echo e($ad->advertisement_type->label()); ?>

                                                </span>
                                                <?php if($ad->package): ?>
                                                <span class="badge bg-primary bg-opacity-10 text-black">
                                                    <?php echo e($ad->package->price); ?> د.ل
                                                </span>
                                                <span class="badge bg-primary bg-opacity-10 text-black">
                                                    <?php echo e($ad->package->title); ?>

                                                </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="text-end">
                                            <small class="text-muted d-block"><?php echo e(__('admin_labels.store_name')); ?></small>
                                            <span class="fw-medium"><?php echo e($ad->storeData?->name_ar ?? '-'); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Footer with Dates and Actions -->
                            <div class="d-flex justify-content-between align-items-center mt-3 pt-2 border-top">
                                <div class="small text-muted">
                                    <span class="me-3"><i class="bx bx-calendar me-1"></i> <?php echo e($ad->start_date); ?></span>
                                    <span><i class="bx bx-calendar-event me-1"></i> <?php echo e($ad->end_date); ?></span>
                                </div>

                                <div class="d-flex gap-2">
                                    <a href="<?php echo e(route('admin.requested.show', $ad->id)); ?>"
                                        class="btn btn-sm btn-outline-primary px-3 rounded-2">
                                        <i class="bx bx-show me-1"></i> <?php echo e(__('admin_labels.view')); ?>

                                    </a>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bx bx-package text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3"><?php echo e(__('admin_labels.no_requested_ads')); ?></h5>
                        <p class="text-muted"><?php echo e(__('admin_labels.no_ads_description')); ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if($ads->hasPages()): ?>
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($ads->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .hover-lift {
        transition: all 0.25s ease;
    }

    .hover-lift:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
    }

    .badge {
        font-weight: 500;
        padding: 0.35em 0.65em;
    }
</style>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/pages/forms/requested_ads.blade.php ENDPATH**/ ?>