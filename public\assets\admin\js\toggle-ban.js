$(document).on("change", ".toggle-ban", function (event) {
    event.preventDefault();

    const checkbox = $(this);
    const userId = checkbox.data("id");
    const isBanned = checkbox.is(":checked");
    const actionText = isBanned ? "حظر" : "رفع الحظر";

    Swal.fire({
        title: `هل أنت متأكد من ${actionText} هذا المستخدم؟`,
        text: isBanned ? "لن يتمكن من استخدام النظام بعد الآن!" : "سيعود المستخدم للنظام!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: `نعم، ${actionText}`,
        cancelButtonText: "إلغاء",
        showLoaderOnConfirm: true,
        preConfirm: () => {
            return $.ajax({
                url: `/admin/customers/${userId}/toggle-ban`,
                method: "POST",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                    banned: isBanned
                },
                dataType: "json"
            })
            .then(response => {
                if (!response.error) {
                    Swal.fire("تم!", response.message, "success");
                } else {
                    Swal.fire("خطأ!", response.message || "فشل العملية", "error");
                    checkbox.prop("checked", !isBanned); // revert change
                }
            })
            .catch(() => {
                Swal.fire("خطأ!", "حدث خطأ أثناء تنفيذ العملية", "error");
                checkbox.prop("checked", !isBanned); // revert change
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    });
});
