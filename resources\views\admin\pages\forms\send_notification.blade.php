@extends('admin/layout')
@section('title')
    {{ labels('admin_labels.notifications', 'Send Notifications') }}
@endsection
@section('content')
    <div class="d-flex row align-items-center">
        <div class="col-md-6 col-xl-6 page-info-title">
            <h3>{{ labels('admin_labels.notifications', 'Notifications') }}
            </h3>
            <p class="sub_title">
                {{ labels('admin_labels.effortlessly_reach_users_with_swift_notification_delivery', 'Effortlessly Reach Users with Swift Notification Delivery') }}
            </p>
        </div>
        <div class="col-md-6 col-xl-6 d-flex justify-content-end" dir='ltr'>
            <nav aria-label="breadcrumb" class="float-end">
                <ol class="breadcrumb">
                    <i class='bx bx-home-smile'></i>
                    <li class="breadcrumb-item"><a href="{{ route('admin.home') }}">{{ labels('admin_labels.home', 'Home') }}</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        {{ labels('admin_labels.notifications', 'Notifications') }}
                    </li>
                </ol>
            </nav>
        </div>
    </div>
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-12 col-lg-4">
                <div class="card card-info">
                    <form class="form-horizontal submit_form" action="{{ route('notifications.store') }}" method="POST"
                        id="" enctype="multipart/form-data">
                        @csrf
                        <div class="card-body">
                            <h5 class="mb-3">
                                {{ labels('admin_labels.send_notification', 'Send Notifications') }}
                            </h5>
                            <div class="form-group">
                                <label for="send_to"
                                    class="control-label mb-2 mt-2">{{ labels('admin_labels.send_to', 'Send to') }}<span
                                        class='text-asterisks text-sm'>*</span></label>
                                <select name="send_to" id="send_to" class="form-control form-select type_event_trigger"
                                    required>
                                    <option value="all_users">{{__('admin_labels.all_users')}}</option>
                                    <option value="specific_user">{{__('admin_labels.specific_user')}}</option>
                                </select>
                            </div>
                            <div class="form-group row notification-users d-none">
                                <label for="user_id"
                                    class="col-md-12 control-label">{{ labels('admin_labels.users', 'Users') }}
                                    <span class='text-asterisks text-sm'>*</span></label>
                                <div class="col-md-12">
                                    <input type="hidden" name="user_id" id="noti_user_id" value="">
                                    <select name="select_user_id[]" class="search_user w-100" multiple
                                        data-placeholder="Type to search and select users" onload="multiselect()">
                                        <!-- Users options here -->
                                    </select>
                                </div>
                            </div>
                           

                         
                            <div class="form-group">
                                <label for="title"
                                    class="control-label mb-2 mt-2">{{ labels('admin_labels.title', 'Title') }}
                                    <span class='text-asterisks text-sm'>*</span></label>
                                <input type="text" class="form-control" name="title" id="title"
                                    value="<?= isset($fetched_data[0]['title']) ? $fetched_data[0]['title'] : '' ?>">
                            </div>

                            <div class="form-group">
                                <label for="message"
                                    class="control-label mb-2 mt-2">{{ labels('admin_labels.message', 'Message') }}
                                    <span class='text-asterisks text-sm'>*</span></label>
                                <textarea name='message' class="form-control"></textarea>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="reset"
                                    class="btn mx-2 reset_button">{{ labels('admin_labels.reset', 'Reset') }}</button>
                                <button type="submit"
                                    class="btn btn-primary submit_button">{{ labels('admin_labels.send_notification', 'Send Notification') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-lg-8 col-md-12 mt-md-2 mt-sm-2 {{  $user_role== 'super_admin' || $logged_in_user->hasPermissionTo('view send_notification') ? '' : 'd-none' }}">
                <section class="overview-data">
                    <div class="card content-area p-4 ">
                        <div class="row align-items-center d-flex heading mb-5">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <h4>{{ labels('admin_labels.notifications', 'Notifications') }}
                                        </h4>
                                    </div>
                                        <div class="col-sm-12 d-flex justify-content-end mt-md-0 mt-sm-2">
                                            <div class="input-group me-2 search-input-grp " dir="ltr">
                                                <span class="search-icon"><i class='bx bx-search-alt'></i></span>
                                                <input type="text" data-table="admin_notification_table"
                                                    class="form-control searchInput" placeholder="{{ labels('admin_labels.search', 'Search') }}...">
                                                <span
                                                    class="input-group-text">{{ labels('admin_labels.search', 'Search') }}</span>
                                            </div>
                                            <a class="btn me-2" id="tableFilter" data-bs-toggle="offcanvas"
                                                data-bs-target="#columnFilterOffcanvas"
                                                data-table="admin_notification_table" dateFilter='false'
                                                orderStatusFilter='false' paymentMethodFilter='false'
                                                orderTypeFilter='false'><i class='bx bx-filter-alt'></i></a>
                                            <a class="btn me-2" id="tableRefresh"data-table="admin_notification_table"><i
                                                    class='bx bx-refresh'></i></a>
                                            <div class="dropdown">
                                                <a class="btn dropdown-toggle export-btn" type="button"
                                                    id="exportOptionsDropdown" data-bs-toggle="dropdown"
                                                    aria-expanded="false">
                                                    <i class='bx bx-download'></i>
                                                </a>
                                                <ul class="dropdown-menu" aria-labelledby="exportOptionsDropdown">
                                                    <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_notification_table','csv')">CSV</button></li>
                                                    <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_notification_table','json')">JSON</button></li>
                                                    <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_notification_table','sql')">SQL</button></li>
                                                    <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_notification_table','excel')">Excel</button></li>
                                                </ul>
                                            </div>
                                        </div>
                                </div>
                            </div>
                        </div>
                            <div class="row">
                                
                                <div class="col-md-12">
                                    <div class="pt-0">
                                        <div class="table-responsive">
                                            <table class='table' id="admin_notification_table" data-toggle="table"
                                                data-loading-template="loadingTemplate"
                                                data-url="{{ route('admin.notifications.list') }}"
                                                data-click-to-select="false" data-side-pagination="server"
                                                data-pagination="true" data-page-list="[5, 10, 20, 50, 100, 200]"
                                                data-search="false" data-show-columns="false" data-show-refresh="false"
                                                data-trim-on-search="false" data-sort-name="id" data-sort-order="desc"
                                                data-mobile-responsive="true" data-toolbar="" data-show-export="false"
                                                data-maintain-selected="true" data-export-types='["txt","excel"]'
                                                data-query-params="queryParams">
                                                <thead>
                                                    <tr>
                                                      
                                                        <th data-field="id" data-sortable="true">
                                                            {{ labels('admin_labels.id', 'ID') }}
                                                        </th>
                                                        <th data-field="title" data-disabled="1" data-sortable="false">
                                                            {{ labels('admin_labels.title', 'Title') }}
                                                        </th>
                                                        <th data-field="type" data-sortable="false">
                                                            {{ labels('admin_labels.type', 'Type') }}
                                                        </th>
                                                        
                                                      
                                                        <th data-field="message" data-sortable="false">
                                                            {{ labels('admin_labels.message', 'Message') }}
                                                        </th>
                                                        <th data-field="send_to" data-sortable="false">
                                                            {{ labels('admin_labels.title', 'Title') }}
                                                        </th>

                                                     
                                                    </tr>
                                                </thead>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
@endsection
